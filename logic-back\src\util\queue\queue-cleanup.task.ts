import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { RedisService } from '../database/redis/redis.service';
import { Redis } from 'ioredis';

/**
 * BullMQ队列清理定时任务
 * 确保所有BullMQ相关的Redis键都有10分钟的过期时间
 */
@Injectable()
export class QueueCleanupTask {
  private readonly logger = new Logger(QueueCleanupTask.name);
  private get redisClient(): Redis {
    return this.redisService.getClient();
  }

  constructor(private readonly redisService: RedisService) {}

  /**
   * 每10分钟执行一次，确保所有BullMQ键都有过期时间
   */
  @Cron('0 */10 * * * *') // 每10分钟执行一次
  async ensureBullMQKeysExpire() {
    this.logger.log('🧹 开始检查BullMQ队列键的过期时间设置');
    
    try {
      // 获取所有BullMQ相关的Redis键
      const bullKeys = await this.redisClient.keys('bull:*');

      if (bullKeys.length === 0) {
        this.logger.log('📭 没有找到BullMQ相关的Redis键');
        return;
      }

      let processedCount = 0;
      let alreadySetCount = 0;

      for (const key of bullKeys) {
        try {
          // 检查键的TTL状态
          const ttl = await this.redisClient.ttl(key);

          // 如果键没有设置过期时间（TTL = -1），设置10分钟过期
          if (ttl === -1) {
            await this.redisClient.expire(key, 10 * 60); // 10分钟 = 600秒
            processedCount++;
          } else if (ttl > 0) {
            alreadySetCount++;
          }
          // ttl = -2 表示键不存在，忽略

        } catch (error) {
          this.logger.warn(`⚠️ 处理键 ${key} 时出错: ${error.message}`);
        }
      }
      
      this.logger.log(`✅ BullMQ键过期时间检查完成:`);
      this.logger.log(`   📊 总键数: ${bullKeys.length}`);
      this.logger.log(`   🔧 新设置过期时间: ${processedCount}个`);
      this.logger.log(`   ✔️ 已有过期时间: ${alreadySetCount}个`);
      
    } catch (error) {
      this.logger.error('❌ BullMQ队列键过期时间检查失败', error);
    }
  }

  /**
   * 每小时执行一次，清理已过期的键和空的数据结构
   */
  @Cron('0 0 * * * *') // 每小时执行一次
  async cleanupExpiredAndEmptyKeys() {
    this.logger.log('🗑️ 开始清理过期和空的BullMQ键');
    
    try {
      const bullKeys = await this.redisClient.keys('bull:*');
      let deletedCount = 0;
      let emptyCount = 0;

      for (const key of bullKeys) {
        try {
          // 检查键是否存在
          const exists = await this.redisClient.exists(key);
          if (!exists) {
            continue;
          }

          // 检查键的类型和内容
          const keyType = await this.redisClient.type(key);
          let isEmpty = false;

          switch (keyType) {
            case 'list':
              const listLength = await this.redisClient.llen(key);
              isEmpty = listLength === 0;
              break;
            case 'set':
              const setSize = await this.redisClient.scard(key);
              isEmpty = setSize === 0;
              break;
            case 'zset':
              const zsetSize = await this.redisClient.zcard(key);
              isEmpty = zsetSize === 0;
              break;
            case 'hash':
              const hashSize = await this.redisClient.hlen(key);
              isEmpty = hashSize === 0;
              break;
          }

          // 删除空的数据结构
          if (isEmpty) {
            await this.redisClient.del(key);
            emptyCount++;
          }

        } catch (error) {
          this.logger.warn(`⚠️ 清理键 ${key} 时出错: ${error.message}`);
        }
      }
      
      this.logger.log(`🧹 BullMQ键清理完成:`);
      this.logger.log(`   🗑️ 删除空键: ${emptyCount}个`);
      
    } catch (error) {
      this.logger.error('❌ BullMQ键清理失败', error);
    }
  }

  /**
   * 手动清理指定队列的键
   */
  async manualCleanupQueue(queueName: string) {
    this.logger.log(`🔧 开始手动清理队列: ${queueName}`);
    
    try {
      const pattern = `bull:${queueName}:*`;
      const keys = await this.redisClient.keys(pattern);

      if (keys.length === 0) {
        return { success: true, message: `队列 ${queueName} 没有找到相关键`, processedKeys: 0 };
      }

      let processedCount = 0;

      for (const key of keys) {
        try {
          const ttl = await this.redisClient.ttl(key);

          // 设置10分钟过期时间
          if (ttl === -1) {
            await this.redisClient.expire(key, 10 * 60);
            processedCount++;
          }
        } catch (error) {
          this.logger.warn(`⚠️ 处理键 ${key} 时出错: ${error.message}`);
        }
      }
      
      const message = `队列 ${queueName} 清理完成，处理了 ${processedCount}/${keys.length} 个键`;
      this.logger.log(`✅ ${message}`);
      
      return { 
        success: true, 
        message, 
        processedKeys: processedCount,
        totalKeys: keys.length 
      };
      
    } catch (error) {
      const errorMessage = `手动清理队列 ${queueName} 失败: ${error.message}`;
      this.logger.error(`❌ ${errorMessage}`);
      return { success: false, error: errorMessage };
    }
  }

  /**
   * 获取BullMQ队列的统计信息
   */
  async getQueueStats() {
    try {
      const bullKeys = await this.redisClient.keys('bull:*');

      const stats = {
        totalKeys: bullKeys.length,
        byQueue: {} as Record<string, number>,
        byType: {} as Record<string, number>,
        ttlStatus: {
          withTTL: 0,      // 有过期时间的键
          withoutTTL: 0,   // 没有过期时间的键
          expired: 0       // 已过期的键
        }
      };

      for (const key of bullKeys) {
        try {
          // 解析队列名称和类型
          const parts = key.split(':');
          if (parts.length >= 3) {
            const queueName = parts[1];
            const keyType = parts[2];

            stats.byQueue[queueName] = (stats.byQueue[queueName] || 0) + 1;
            stats.byType[keyType] = (stats.byType[keyType] || 0) + 1;
          }

          // 检查TTL状态
          const ttl = await this.redisClient.ttl(key);
          if (ttl === -1) {
            stats.ttlStatus.withoutTTL++;
          } else if (ttl === -2) {
            stats.ttlStatus.expired++;
          } else {
            stats.ttlStatus.withTTL++;
          }

        } catch (error) {
          this.logger.warn(`⚠️ 获取键 ${key} 统计信息时出错: ${error.message}`);
        }
      }

      return stats;
    } catch (error) {
      this.logger.error('❌ 获取BullMQ队列统计信息失败', error);
      return null;
    }
  }
}
