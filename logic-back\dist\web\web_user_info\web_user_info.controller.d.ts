import { HttpStatus } from '@nestjs/common';
import { WebUserInfoService } from './web_user_info.service';
import { UserInfoService } from 'src/util/database/mysql/user_info/user_info.service';
import { UserPasswordResetRequestService } from 'src/util/database/mysql/user_password_reset_request/user_password_reset_request.service';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
export declare class WebUserInfoController {
    private readonly webUserInfoService;
    private readonly userInfoService;
    private readonly passwordResetRequestService;
    private readonly httpResponseResultService;
    constructor(webUserInfoService: WebUserInfoService, userInfoService: UserInfoService, passwordResetRequestService: UserPasswordResetRequestService, httpResponseResultService: HttpResponseResultService);
    ping(): Promise<import("../http_response_result/http-response.interface").HttpResponse<{
        msg: string;
        data: {
            timestamp: number;
            status: string;
        };
    }>>;
    checkHasBindPhone(selectedStudents: number[]): Promise<import("../http_response_result/http-response.interface").HttpResponse<{
        code: number;
        data: string[];
        message: string;
        msg?: undefined;
    } | {
        code: number;
        msg: string;
        data?: undefined;
        message?: undefined;
    }>>;
    getStudentInfo(id: number): Promise<{
        needSetPwd: boolean;
        id: number;
        avatarUrl: string;
        nickName: string;
        phone: string;
        password: string;
        points: number;
        pointsExpireTime: Date;
        introduction: string;
        roleId: number;
        role: import("../../util/database/mysql/user_role/entities/user_role.entity").UserRole;
        weixin_openid: string;
        gender: number;
        register_type: string;
        status: number;
        student: import("../../util/database/mysql/user_student/entities/user_student.entity").UserStudent;
        createTime: Date;
        updateTime: Date;
    }>;
    updateUserInfo(updateData: {
        id: number;
        nickName?: string;
        avatarUrl?: string;
        introduction?: string;
    }): Promise<{
        nickName: string;
        avatarUrl: string;
        introduction: string;
    }>;
    updatePassword(passwordData: {
        id: number;
        oldPassword: string;
        newPassword: string;
    }): Promise<{
        code: HttpStatus;
        msg: string;
        data: null;
    }>;
    requestPasswordReset(requestData: {
        phone: string;
        name: string;
        contactInfo?: string;
        remark?: string;
        schoolName?: string;
        studentNumber?: string;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<{
        msg: string;
        data: {
            requestId: number;
        };
    }>>;
    handlePasswordReset(handleData: {
        requestId: number;
        status: number;
        handleRemark?: string;
        handlerId: number;
    }): Promise<import("../http_response_result/http-response.interface").HttpResponse<null>>;
    getPasswordResetList(status?: number, page?: number, size?: number): Promise<{
        code: HttpStatus;
        data: {
            list: any[];
            pagination: {
                page: number;
                size: number;
                total: number;
            };
        };
    }>;
}
