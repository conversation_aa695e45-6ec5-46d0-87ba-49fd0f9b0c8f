"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var EncryptionCleanupTask_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.EncryptionCleanupTask = void 0;
const common_1 = require("@nestjs/common");
const schedule_1 = require("@nestjs/schedule");
const encryption_service_1 = require("./encryption.service");
let EncryptionCleanupTask = EncryptionCleanupTask_1 = class EncryptionCleanupTask {
    encryptionService;
    logger = new common_1.Logger(EncryptionCleanupTask_1.name);
    constructor(encryptionService) {
        this.encryptionService = encryptionService;
        this.logger.log('【初始化】会话清理任务已启动');
    }
    async handleSessionCleanup() {
        this.logger.log('【定时任务】开始执行会话清理任务');
        try {
            await this.encryptionService.cleanupExpiredSessions();
            this.logger.log('【定时任务】会话清理任务完成');
        }
        catch (error) {
            this.logger.error(`【定时任务】会话清理任务失败: ${error.message}`, error.stack);
        }
    }
    async logDailyStats() {
        this.logger.log('【定时任务】每日统计 - 开始');
        try {
            await this.encryptionService.logSessionStats();
            this.logger.log('【定时任务】每日统计 - 完成');
        }
        catch (error) {
            this.logger.error(`【定时任务】每日统计失败: ${error.message}`, error.stack);
        }
    }
};
exports.EncryptionCleanupTask = EncryptionCleanupTask;
__decorate([
    (0, schedule_1.Cron)('0 0 */1 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EncryptionCleanupTask.prototype, "handleSessionCleanup", null);
__decorate([
    (0, schedule_1.Cron)('0 0 3 * * *'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], EncryptionCleanupTask.prototype, "logDailyStats", null);
exports.EncryptionCleanupTask = EncryptionCleanupTask = EncryptionCleanupTask_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [encryption_service_1.EncryptionService])
], EncryptionCleanupTask);
//# sourceMappingURL=cleanup.task.js.map