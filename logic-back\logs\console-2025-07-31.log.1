2025-07-31 17:07:51.724 [INFO] [RouterExplorer] Mapped {/api/v1/activity/myRegistrations, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.730 [INFO] [RouterExplorer] Mapped {/api/v1/activity/registrationStatistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.730 [INFO] [RoutesResolver] ActivityController {/activity}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.730 [INFO] [RouterExplorer] Mapped {/activity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.730 [INFO] [RouterExplorer] Mapped {/activity, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.731 [INFO] [RouterExplorer] Mapped {/activity/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.731 [INFO] [RouterExplorer] Mapped {/activity/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.731 [INFO] [RouterExplorer] Mapped {/activity/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.731 [INFO] [RouterExplorer] Mapped {/activity/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.732 [INFO] [RouterExplorer] Mapped {/activity/type/:activityType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.732 [INFO] [RouterExplorer] Mapped {/activity/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.732 [INFO] [RouterExplorer] Mapped {/activity/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.732 [INFO] [RouterExplorer] Mapped {/activity/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.732 [INFO] [RouterExplorer] Mapped {/activity/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.732 [INFO] [RouterExplorer] Mapped {/activity/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.733 [INFO] [RoutesResolver] ActivitySubmitController {/activity-submit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.733 [INFO] [RouterExplorer] Mapped {/activity-submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.733 [INFO] [RouterExplorer] Mapped {/activity-submit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.733 [INFO] [RouterExplorer] Mapped {/activity-submit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.733 [INFO] [RouterExplorer] Mapped {/activity-submit/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.733 [INFO] [RouterExplorer] Mapped {/activity-submit/check/:activityId/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.734 [INFO] [RouterExplorer] Mapped {/activity-submit/statistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.734 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.734 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.734 [INFO] [RouterExplorer] Mapped {/activity-submit/:id/cancel, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.734 [INFO] [RouterExplorer] Mapped {/activity-submit/cancel/:activityId/:userId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.735 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.735 [INFO] [RoutesResolver] ActivityEventsTaskController {/activity-events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.735 [INFO] [RouterExplorer] Mapped {/activity-events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.735 [INFO] [RouterExplorer] Mapped {/activity-events-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.735 [INFO] [RouterExplorer] Mapped {/activity-events-task/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.736 [INFO] [RouterExplorer] Mapped {/activity-events-task/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.736 [INFO] [RouterExplorer] Mapped {/activity-events-task/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.736 [INFO] [RouterExplorer] Mapped {/activity-events-task/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.736 [INFO] [RouterExplorer] Mapped {/activity-events-task/school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.736 [INFO] [RouterExplorer] Mapped {/activity-events-task/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.736 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.737 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.737 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.737 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.737 [INFO] [RoutesResolver] WebActivityTagController {/api/v1/activity_tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.737 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/add-tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.737 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/edit-tags/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.737 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/remove-tags/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.738 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-activity/:activityId/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.738 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-tag/:tagId/activities, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.738 [INFO] [RoutesResolver] ActivityTagController {/activity-tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.738 [INFO] [RouterExplorer] Mapped {/activity-tag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.738 [INFO] [RouterExplorer] Mapped {/activity-tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.738 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.739 [INFO] [RouterExplorer] Mapped {/activity-tag/tag/:tagId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.739 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.739 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.739 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.739 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.739 [INFO] [RouterExplorer] Mapped {/activity-tag/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.740 [INFO] [RoutesResolver] WebActivityWorkController {/api/v1/activity_work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.740 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-works, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.740 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-image, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.740 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-works/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.740 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/check-submitted/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.741 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/list/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.741 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/remove/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.741 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/batch-remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.741 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/set-awarded/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.741 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-category/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.741 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-status/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.742 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/cancel/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.742 [INFO] [RoutesResolver] ActivityWorkController {/activity-work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.742 [INFO] [RouterExplorer] Mapped {/activity-work, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.742 [INFO] [RouterExplorer] Mapped {/activity-work, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.742 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.742 [INFO] [RouterExplorer] Mapped {/activity-work/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.743 [INFO] [RouterExplorer] Mapped {/activity-work/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.743 [INFO] [RouterExplorer] Mapped {/activity-work/selected/:isSelected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.743 [INFO] [RouterExplorer] Mapped {/activity-work/winners, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.743 [INFO] [RouterExplorer] Mapped {/activity-work/:id/selected/:isSelected, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.743 [INFO] [RouterExplorer] Mapped {/activity-work/:id/winner/:isWinner, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.744 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.744 [INFO] [RouterExplorer] Mapped {/activity-work/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.744 [INFO] [RouterExplorer] Mapped {/activity-work/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.744 [INFO] [RouterExplorer] Mapped {/activity-work/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.744 [INFO] [RouterExplorer] Mapped {/activity-work/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.745 [INFO] [RoutesResolver] WebEventsTaskController {/api/v1/web/events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.745 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.745 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.745 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/activity/:activityId/tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.745 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.746 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.746 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/ongoing, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.746 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.746 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.746 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.747 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.747 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.747 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/admin-review, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.747 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.747 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.748 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/batch/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.748 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.748 [INFO] [RoutesResolver] ManagementController {/api/v1/course-management}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.748 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/my-series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.749 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/courses, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.749 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.749 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.750 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.750 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.750 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.751 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.751 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/settings, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.751 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/task-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.751 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.752 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.752 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.752 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/course-orders, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.752 [INFO] [RoutesResolver] TeachingController {/api/v1/course-teaching}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.752 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/one-click-start, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.753 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/course-settings/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.753 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.753 [INFO] [RoutesResolver] MarketplaceController {/api/v1/course-marketplace}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.753 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.753 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.754 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId/courses/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.754 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.754 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.754 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.754 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.754 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.755 [INFO] [RoutesResolver] AiImageGenerateController {/api/ai-image-generate}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.755 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.755 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.755 [INFO] [RoutesResolver] QueueController {/api/queue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.755 [INFO] [RouterExplorer] Mapped {/api/queue/addJob, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.755 [INFO] [RouterExplorer] Mapped {/api/queue/clearAllQueues, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.756 [INFO] [RouterExplorer] Mapped {/api/queue/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.756 [INFO] [RouterExplorer] Mapped {/api/queue/cleanup/:queueName, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.756 [INFO] [RouterExplorer] Mapped {/api/queue/ensure-expire, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.756 [INFO] [RoutesResolver] MinimaxImageController {/api/minimax-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.756 [INFO] [RouterExplorer] Mapped {/api/minimax-image/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.756 [INFO] [RoutesResolver] AiTextDialogueController {/api/ai-text-dialogue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.756 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.757 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.757 [INFO] [RoutesResolver] ZhipuLlmController {/zhipu-llm}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.757 [INFO] [RouterExplorer] Mapped {/zhipu-llm/chat, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.757 [INFO] [RoutesResolver] AliQwenTurboController {/ali-qwen-turbo}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.758 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/stream, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.758 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/sse-callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.758 [INFO] [RoutesResolver] AiVisualRecognitionController {/api/ai-visual-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.759 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.759 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.759 [INFO] [RoutesResolver] AliQwenVisionController {/ali-qwen-vision}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.759 [INFO] [RouterExplorer] Mapped {/ali-qwen-vision/analyze, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.759 [INFO] [RoutesResolver] AiExpressionRecognitionController {/api/ai-expression-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.759 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.760 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.760 [INFO] [RoutesResolver] AliyunExpressionController {/api/aliyun-expression}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.760 [INFO] [RouterExplorer] Mapped {/api/aliyun-expression/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.760 [INFO] [RoutesResolver] AiFaceCompareController {/ai-face-compare}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.760 [INFO] [RouterExplorer] Mapped {/ai-face-compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.760 [INFO] [RouterExplorer] Mapped {/ai-face-compare/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.761 [INFO] [RoutesResolver] AliyunFaceCompareController {/aliyun-face-one-contrast-one}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.761 [INFO] [RouterExplorer] Mapped {/aliyun-face-one-contrast-one/compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.761 [INFO] [RoutesResolver] AiFaceRecognitionController {/api/ai-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.761 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.762 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.762 [INFO] [RoutesResolver] AliyunFaceRecognitionController {/api/aliyun-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.762 [INFO] [RouterExplorer] Mapped {/api/aliyun-face-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.762 [INFO] [RoutesResolver] AiImageEnhanceController {/api/ai-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.762 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.762 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.763 [INFO] [RoutesResolver] BaiduImageEnhanceController {/api/baidu-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.763 [INFO] [RouterExplorer] Mapped {/api/baidu-image-enhance/enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.763 [INFO] [RoutesResolver] AiImageScoreController {/api/ai-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.763 [INFO] [RouterExplorer] Mapped {/api/ai-image-score, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.763 [INFO] [RouterExplorer] Mapped {/api/ai-image-score/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.763 [INFO] [RoutesResolver] AliyunImageScoreController {/api/aliyun-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.764 [INFO] [RouterExplorer] Mapped {/api/aliyun-image-score/assess, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.764 [INFO] [RoutesResolver] AiImageSegmentController {/api/ai-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.764 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.764 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.764 [INFO] [RoutesResolver] AliyunSegmentImageController {/api/aliyun-segment-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.765 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.765 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/download, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.765 [INFO] [RoutesResolver] AiSpeechSynthesisController {/api/ai-speech-synthesis}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.765 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.765 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.766 [INFO] [RoutesResolver] MinimaxTtsController {/api/minimax-tts}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.766 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/config, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.766 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.766 [INFO] [RoutesResolver] AiSpeechRecognitionController {/api/ai-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.766 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.766 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.767 [INFO] [RoutesResolver] XunfeiSpeechRecognitionController {/xunfei-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.767 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-url, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.767 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-base64, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.767 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-file, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.767 [INFO] [RoutesResolver] TrainImageController {/api/scratch/train/image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.767 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.768 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.768 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.768 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.768 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.768 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.768 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.769 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.769 [INFO] [RoutesResolver] ImageTrainModelController {/image-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.769 [INFO] [RouterExplorer] Mapped {/image-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.769 [INFO] [RouterExplorer] Mapped {/image-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.769 [INFO] [RouterExplorer] Mapped {/image-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.769 [INFO] [RouterExplorer] Mapped {/image-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.769 [INFO] [RouterExplorer] Mapped {/image-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.770 [INFO] [RouterExplorer] Mapped {/image-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.770 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.770 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.770 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.770 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.771 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.771 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.771 [INFO] [RoutesResolver] TrainPoseController {/api/scratch/train/pose}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.771 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.771 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.772 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.772 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.772 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.772 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.772 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.772 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.773 [INFO] [RoutesResolver] PoseTrainModelController {/pose-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.773 [INFO] [RouterExplorer] Mapped {/pose-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.773 [INFO] [RouterExplorer] Mapped {/pose-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.773 [INFO] [RouterExplorer] Mapped {/pose-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.773 [INFO] [RouterExplorer] Mapped {/pose-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.773 [INFO] [RouterExplorer] Mapped {/pose-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.773 [INFO] [RouterExplorer] Mapped {/pose-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.774 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.774 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.774 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id/increment-use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.774 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.774 [INFO] [RoutesResolver] TrainSoundController {/api/scratch/train/sound}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.774 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.775 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.775 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.775 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.775 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.775 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.775 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.776 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.776 [INFO] [RoutesResolver] AudioTrainModelController {/audio-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.776 [INFO] [RouterExplorer] Mapped {/audio-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.776 [INFO] [RouterExplorer] Mapped {/audio-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.776 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.776 [INFO] [RouterExplorer] Mapped {/audio-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.777 [INFO] [RouterExplorer] Mapped {/audio-train-model/type/:modelType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.777 [INFO] [RouterExplorer] Mapped {/audio-train-model/public/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.777 [INFO] [RouterExplorer] Mapped {/audio-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.777 [INFO] [RouterExplorer] Mapped {/audio-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.777 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.778 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.778 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.778 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.778 [INFO] [RoutesResolver] AiObjectDetectionController {/api/ai-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.778 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.778 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.779 [INFO] [RoutesResolver] AliyunObjectDetectionController {/api/aliyun-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.779 [INFO] [RouterExplorer] Mapped {/api/aliyun-object-detection/detect, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.779 [INFO] [RoutesResolver] AiStaticGestureRecognitionController {/api/scratch/ai-static-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.779 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.779 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.780 [INFO] [RoutesResolver] AliyunStaticGestureRecognitionController {/api/aliyun-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.780 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.780 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.780 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.780 [INFO] [RoutesResolver] UserPointsOfflineMessageController {/user-points-offline-message}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.780 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.781 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.781 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.781 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/pending, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.781 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.781 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.781 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id/mark-as-sent, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.782 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.782 [INFO] [RoutesResolver] UserRolePermissionController {/user-role-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.782 [INFO] [RouterExplorer] Mapped {/user-role-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.782 [INFO] [RouterExplorer] Mapped {/user-role-permission/batch-assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.782 [INFO] [RouterExplorer] Mapped {/user-role-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.782 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.783 [INFO] [RouterExplorer] Mapped {/user-role-permission/permission/:permissionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.783 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.783 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.783 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.783 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/permission/:permissionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.783 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.784 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.784 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.784 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.784 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.784 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.784 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.785 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.785 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.785 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.785 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.785 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.785 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.786 [INFO] [RoutesResolver] UserSchoolRelationController {/user-school-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.786 [INFO] [RouterExplorer] Mapped {/user-school-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.786 [INFO] [RouterExplorer] Mapped {/user-school-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.786 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.786 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.786 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.787 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.787 [INFO] [RouterExplorer] Mapped {/user-school-relation/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.787 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.787 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.787 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id/role-type/:roleType, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.787 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.787 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/school/:schoolId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.788 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.788 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.788 [INFO] [RoutesResolver] UserWorkLikeController {/user-work-like}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.788 [INFO] [RouterExplorer] Mapped {/user-work-like, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.788 [INFO] [RouterExplorer] Mapped {/user-work-like, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.788 [INFO] [RouterExplorer] Mapped {/user-work-like/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.788 [INFO] [RouterExplorer] Mapped {/user-work-like/target/:targetId/type/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.789 [INFO] [RouterExplorer] Mapped {/user-work-like/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.789 [INFO] [RouterExplorer] Mapped {/user-work-like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.789 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.789 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.789 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.789 [INFO] [RoutesResolver] ActivityAuditController {/activity-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.789 [INFO] [RouterExplorer] Mapped {/activity-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.790 [INFO] [RouterExplorer] Mapped {/activity-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.790 [INFO] [RouterExplorer] Mapped {/activity-audit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.790 [INFO] [RouterExplorer] Mapped {/activity-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.790 [INFO] [RouterExplorer] Mapped {/activity-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.790 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.791 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.791 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.791 [INFO] [RouterExplorer] Mapped {/activity-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.791 [INFO] [RoutesResolver] ParticipationAuditController {/participation-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.792 [INFO] [RouterExplorer] Mapped {/participation-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.793 [INFO] [RouterExplorer] Mapped {/participation-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.794 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.794 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.794 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.794 [INFO] [RoutesResolver] WorkAuditController {/work-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.794 [INFO] [RouterExplorer] Mapped {/work-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.795 [INFO] [RouterExplorer] Mapped {/work-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.795 [INFO] [RouterExplorer] Mapped {/work-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.795 [INFO] [RouterExplorer] Mapped {/work-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.796 [INFO] [RouterExplorer] Mapped {/work-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.796 [INFO] [RoutesResolver] StudentSelfAssessmentSubmissionController {/student-self-assessment-submission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.796 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/bulk, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.796 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/assignment/:assignmentId/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.796 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/item/:itemId/submissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.797 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.797 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.797 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.798 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.798 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.798 [INFO] [RoutesResolver] EncryptionController {/api/encryption}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.799 [INFO] [RouterExplorer] Mapped {/api/encryption/publicKey, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.799 [INFO] [RouterExplorer] Mapped {/api/encryption/keyStatus, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.800 [INFO] [RouterExplorer] Mapped {/api/encryption/session, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.800 [INFO] [RouterExplorer] Mapped {/api/encryption/secureSession, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.800 [INFO] [RouterExplorer] Mapped {/api/encryption/renew-session/:sessionId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.801 [INFO] [RouterExplorer] Mapped {/api/encryption/session/:sessionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.801 [INFO] [RouterExplorer] Mapped {/api/encryption/session-stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.801 [INFO] [RouterExplorer] Mapped {/api/encryption/cleanup-sessions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.802 [INFO] [RouterExplorer] Mapped {/api/encryption/create-session-debug, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.802 [INFO] [RoutesResolver] EncryptExampleController {/encrypt-example}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.802 [INFO] [RouterExplorer] Mapped {/encrypt-example/public-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.803 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.803 [INFO] [RouterExplorer] Mapped {/encrypt-example/partial-secure/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.803 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-with-decrypt, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.803 [INFO] [RouterExplorer] Mapped {/encrypt-example/test-request-body-encryption, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.804 [INFO] [RouterExplorer] Mapped {/encrypt-example/simple-partial/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.804 [INFO] [RoutesResolver] SecureExampleController {/api/secure-examples}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.804 [INFO] [RouterExplorer] Mapped {/api/secure-examples/standard, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.804 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.804 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure-partial, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.804 [INFO] [RouterExplorer] Mapped {/api/secure-examples/sessions/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.805 [INFO] [RoutesResolver] IpLocationController {/api/v1/ip-location}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.805 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/query, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.805 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/check-risk, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.805 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.805 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/user/:userId/trust, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.805 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/current, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.806 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/health, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.806 [INFO] [RoutesResolver] TPSController {/tps}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.806 [INFO] [RouterExplorer] Mapped {/tps/create-test-students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.806 [INFO] [RouterExplorer] Mapped {/tps/delete-by-prefix, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.806 [INFO] [RouterExplorer] Mapped {/tps/delete-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.807 [INFO] [RouterExplorer] Mapped {/tps/create-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.807 [INFO] [RouterExplorer] Mapped {/tps/create-test-teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.807 [INFO] [RouterExplorer] Mapped {/tps/export-test-data-csv, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.807 [INFO] [RouterExplorer] Mapped {/tps/assign-special-package-to-all-students, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.807 [INFO] [RoutesResolver] AiVoiceprintRecognitionController {/api/ai-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.807 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.808 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.808 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.808 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.808 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.808 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-libraries, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.809 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.809 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.809 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.809 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.809 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-features/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.810 [INFO] [RoutesResolver] XunfeiVoiceprintRecognitionController {/xunfei-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.810 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.810 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.810 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.810 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.811 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.811 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.811 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.811 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.811 [INFO] [RoutesResolver] VoiceprintGroupController {/voiceprint-group}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.812 [INFO] [RouterExplorer] Mapped {/voiceprint-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.812 [INFO] [RouterExplorer] Mapped {/voiceprint-group, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.812 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.812 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.812 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.812 [INFO] [RoutesResolver] VoiceprintFeatureController {/voiceprint-feature}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.813 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.813 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.813 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.813 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/group/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.814 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.814 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.814 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.814 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.814 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.815 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.815 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.815 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.815 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.816 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.816 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.816 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.816 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.816 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.817 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.817 [INFO] [PlatformCertificateService] 已加载微信支付平台证书: 604B78292D99D739AA8F4D437065F4D6A87F4057, 过期时间: 2030-06-30T10:22:39+08:00 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.817 [INFO] [PlatformCertificateService] 已加载 1 个微信支付平台证书 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.817 [INFO] [KeyManagementService] 密钥管理选项已加载: algorithm=aes-256-cbc, useRedis=true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.817 [INFO] [KeyManagementService] 从Redis加载了密钥: f2cd89fd-c713-4da3-81b2-63fbf1d264c0 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.818 [INFO] [KeyManagementService] 从Redis加载了密钥: 18a27d70-6f69-48f5-977d-a8333c052ab3 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.818 [INFO] [KeyManagementService] 从Redis加载了密钥: 401b349c-c3f8-4fee-9ff3-3cfa252b56b7 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.818 [INFO] [KeyManagementService] 从Redis加载了密钥: b0381a63-e168-4575-97d7-a67b68462d98 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.818 [INFO] [KeyManagementService] 从Redis加载了4个密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.818 [INFO] [KeyManagementService] 尝试从Redis加载RSA密钥对... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.819 [INFO] [KeyManagementService] 从Redis加载了1个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.819 [INFO] [KeyManagementService] 已从Redis加载1个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.819 [INFO] [KeyManagementService] 活跃RSA密钥对验证通过 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.819 [INFO] [KeyManagementService] RSA密钥对已初始化，共1个版本，活跃版本: key-006b9ed6 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.820 [INFO] [KeyManagementService] 密钥管理服务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.820 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.820 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.820 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.820 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.821 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.821 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.821 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.821 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.822 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.822 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.822 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.822 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.822 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.823 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.823 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.823 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.823 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.824 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.824 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.824 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.824 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.825 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.825 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.825 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.825 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.825 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.826 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.826 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.826 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.826 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.826 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.827 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.827 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.827 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.827 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.828 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.828 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.828 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.828 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.828 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.829 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.829 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.829 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.829 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.829 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.830 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.830 [INFO] [NestApplication] Nest application successfully started {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:07:51.837 [INFO] [Startup] Application started successfully {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","url":"http://[::1]:8003","port":8003}
