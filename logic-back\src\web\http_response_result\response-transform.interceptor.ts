import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { Request } from 'express';
import { HttpResponse, SUCCESS_CODE } from './http-response.interface';
import { HttpResponseResultService, LogOptions } from './http_response_result.service';

/**
 * 响应转换拦截器
 * 将所有返回结果转换为统一的响应格式 { code, msg, data }，并自动记录响应日志
 */
@Injectable()
export class ResponseTransformInterceptor<T> implements NestInterceptor<T, HttpResponse<T>> {
  constructor(private readonly responseService: HttpResponseResultService) {}
  intercept(context: ExecutionContext, next: CallHandler): Observable<HttpResponse<T>> {
    const request = context.switchToHttp().getRequest<Request>();
    const path = request.path || request.url || '';

    // 记录请求开始时间
    const startTime = Date.now();

    // 排除特殊接口，不进行响应转换
    const excludePaths = ['/weixin/message', '/health', '/metrics'];
    if (excludePaths.some(excludePath => path.includes(excludePath))) {
      return next.handle();
    }

    return next.handle().pipe(
      map(data => {
        // 计算执行时间
        const executionTime = Date.now() - startTime;

        // 构建日志选项（只在生产环境启用）
        const logOptions: LogOptions = {
          path,
          requestId: request.headers['x-request-id'] as string,
          executionTime,
          context: 'ResponseTransformInterceptor',
          enableLog: process.env.NODE_ENV === 'production'
        };

        // 如果已经是标准格式，直接返回（不重复记录日志）
        if (data && typeof data === 'object' && 'code' in data && 'msg' in data && 'data' in data) {
          return data;
        }

        // 如果是加密响应，直接返回（不进行格式转换）
        if (data && typeof data === 'object' && ('encrypted' in data || 'partialEncrypted' in data)) {
          return data;
        }

        // 转换为标准格式并记录日志
        return this.responseService.success(data, '操作成功', SUCCESS_CODE, logOptions);
      }),
    );
  }
} 