2025-07-31 11:47:08.884 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 11:47:08.997 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.997 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.998 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.998 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:08.999 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.000 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.000 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.001 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.001 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.002 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.003 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.004 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.004 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:09.005 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:47:23.717 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/health - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:116:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:22)
    at async GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:16:17)
    at async canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:33)
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:31
    at async F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:17
2025-07-31 11:47:23.719 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/health","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"您的账号已在其他设备登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:47:23.718Z"}
2025-07-31 11:50:29.809 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":1}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:76:20)
2025-07-31 11:50:29.810 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:29.811 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:29.812 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.queryIpLocationV2 (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:29.811Z"}
2025-07-31 11:50:30.637 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:227:20)
2025-07-31 11:50:30.638 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:30.639 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:230:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:30.639 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:230:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:30.639Z"}
2025-07-31 11:50:31.104 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:76:20)
2025-07-31 11:50:31.105 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:31.105 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:31.106 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.queryIpLocationV2 (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:31.105Z"}
2025-07-31 11:50:43.043 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:227:20)
2025-07-31 11:50:43.043 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:43.044 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/current - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.getCurrentIpLocation (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:230:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:43.044 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/current","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.getCurrentIpLocation (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:230:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:43.044Z"}
2025-07-31 11:50:43.842 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":0}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:76:20)
2025-07-31 11:50:43.842 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 11:50:43.843 [ERROR] [GlobalExceptionFilter] GET /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - IP地址不支持地理位置解析 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
    at IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:13)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
2025-07-31 11:50:43.844 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":82452,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","method":"GET","statusCode":500,"message":"IP地址不支持地理位置解析","details":{"name":"Error","stack":"Error: IP地址不支持地理位置解析\n    at IpLocationController.queryIpLocationV2 (F:\\logicleap2\\logic-back\\src\\util\\ip_location\\controllers\\ip-location.controller.ts:83:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)"},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::1","timestamp":"2025-07-31T03:50:43.844Z"}
2025-07-31 11:59:29.200 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 11:59:29.266 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.266 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.266 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.267 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.267 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.267 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.268 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.268 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.268 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.269 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.270 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.270 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.270 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.271 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.271 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.271 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 11:59:29.272 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":8184,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.664 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 12:00:06.750 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.751 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.751 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.752 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.752 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.753 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.754 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.754 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.755 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.755 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.756 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.756 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.757 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.757 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.757 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.758 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.758 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.758 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.759 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:00:06.759 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.238 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 12:04:23.338 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.338 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.339 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.339 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.339 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.340 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.340 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.341 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.341 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.342 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.342 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.342 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.343 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.343 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.344 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.344 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.345 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.345 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.346 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:04:23.346 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":22760,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.200 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 12:05:13.292 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.292 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.292 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.293 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.295 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.296 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.296 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.297 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.298 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.298 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.299 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.299 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.300 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.300 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.301 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.301 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.302 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.303 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.303 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:05:13.304 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78864,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.601 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 12:23:23.682 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.683 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.683 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.684 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.684 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.684 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.685 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.685 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.685 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.686 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.686 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.687 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.687 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.687 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.687 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.688 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.688 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.689 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.689 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:23:23.690 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.550 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 12:24:11.625 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.625 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.625 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.626 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.626 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.627 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.627 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.628 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.628 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.629 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.629 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.630 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.630 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.631 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.631 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.631 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.632 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.632 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.633 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 12:24:11.633 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:11:50.317 [ERROR] [Business] Business IpLocationApplicationService.queryIpLocation failed {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG","module":"IpLocationApplicationService","action":"queryIpLocation","data":"{\"ip\":\"127.0.0.***\",\"responseTime\":1}","error":"IP地址不支持地理位置解析"}
Error: IP地址不支持地理位置解析
    at IpLocationApplicationService.queryIpLocation (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-application.service.ts:86:15)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async IpLocationFacadeService.getLocationByIP (F:\logicleap2\logic-back\src\util\ip_location\application\services\ip-location-facade.service.ts:45:22)
    at async IpLocationController.queryIpLocationV2 (F:\logicleap2\logic-back\src\util\ip_location\controllers\ip-location.controller.ts:83:22)
2025-07-31 14:11:50.318 [ERROR] [IpLocationFacadeService] IP位置查询失败: 127.0.0.1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG"}
Error: IP地址不支持地理位置解析
2025-07-31 14:11:50.319 [ERROR] [IpLocationController.queryIpLocationV2] [ERROR] - IP地址不支持地理位置解析 - Code: 400 - Path: /api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false - Time: 10ms {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 14:11:50.319 [ERROR] [IpLocationController.queryIpLocationV2] {"type":"Error Details","message":"IP地址不支持地理位置解析","code":400,"data":{"ip":"127.0.0.1","includeRisk":false},"path":"/api/v1/ip-location/query?ip=127.0.0.1&includeRisk=false","executionTime":10,"timestamp":"2025-07-31T06:11:50.319Z"} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78560,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 14:16:06.230 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:16:06.320 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.321 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.321 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.322 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.322 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.323 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.323 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.323 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.324 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.324 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.325 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.325 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.326 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.326 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.327 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.327 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.328 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.328 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.329 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:16:06.329 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":40736,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:23.957 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:18:24.040 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.041 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.041 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.042 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.042 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.042 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.043 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.043 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.043 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.044 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.044 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.045 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.045 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.045 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.046 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.046 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.047 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.047 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.047 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:18:24.047 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:22:55.800 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87120,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.439 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:28:00.521 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.521 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.521 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.522 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.522 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.522 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.523 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.523 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.524 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.524 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.524 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.525 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.525 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.526 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.526 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.527 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.527 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.527 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.528 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:00.528 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:28:05.849 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 14:28:05.850 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T06:28:05.850Z"}
2025-07-31 14:28:10.887 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91860,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.084 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:30:36.172 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.172 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.172 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.173 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.174 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.174 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.174 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.175 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.175 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.176 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.176 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.177 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.177 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.178 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.179 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.179 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.180 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.180 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.181 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:30:36.181 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:32:05.325 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 14:32:05.326 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":75504,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T06:32:05.326Z"}
2025-07-31 14:37:54.709 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:37:54.789 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.789 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.789 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.790 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.790 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.791 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.791 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.791 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.792 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.792 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.793 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.793 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.793 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.794 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.794 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.794 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.795 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.795 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.795 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:37:54.796 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":89972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.439 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:38:11.527 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.527 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.528 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.528 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.529 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.529 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.530 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.530 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.531 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.531 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.532 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.532 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.533 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.533 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.533 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.534 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.534 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.535 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.535 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:11.536 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":17164,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.624 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:38:44.699 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.699 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.700 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.700 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.701 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.701 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.701 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.702 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.702 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.703 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.703 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.704 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.704 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.705 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.705 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.706 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.706 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.706 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.707 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:44.707 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:45.786 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:38:51.033 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51940,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.677 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:40:51.773 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.773 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.774 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.775 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.775 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.776 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.776 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.777 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.777 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.778 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.778 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.779 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.779 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.780 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.780 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.781 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.781 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.782 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.782 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:40:51.783 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":43344,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:26.966 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:41:27.078 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.079 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.079 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.080 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.080 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.081 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.081 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.082 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.082 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.083 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.084 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.085 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.085 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.087 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.087 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.088 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.088 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.089 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.089 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:41:27.091 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86528,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.314 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 14:42:12.411 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.412 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.412 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.413 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.413 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.414 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.414 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.415 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.415 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.416 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.416 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.417 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.417 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.418 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.418 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.419 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.419 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.420 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.420 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:42:12.420 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 14:45:14.984 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77584,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.891 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 15:01:36.961 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.961 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.962 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.962 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.962 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.963 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.963 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.964 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.964 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.964 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.965 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.965 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.965 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.966 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.966 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.966 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.967 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.967 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.968 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:01:36.968 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91812,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.663 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 15:02:06.741 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.741 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.742 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.742 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.742 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.743 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.743 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.743 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.744 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.744 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.744 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.745 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.745 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.745 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.746 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.746 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.746 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.746 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.747 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:02:06.747 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":23704,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.715 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 15:08:42.794 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.795 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.795 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.795 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.796 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.796 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.796 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.797 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.797 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.798 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.798 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.798 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.799 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.799 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.799 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.799 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.800 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.800 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.800 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 15:08:42.801 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":55972,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.197 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:12:15.274 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.274 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.275 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.275 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.276 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.276 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.277 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.277 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.277 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.278 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.278 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.279 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.279 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.279 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.280 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.280 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.281 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.281 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.282 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:12:15.282 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":58536,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.086 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:21:09.161 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.162 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.162 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.163 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.164 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.164 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.165 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.166 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.166 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.166 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.167 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.167 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.168 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.168 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.168 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.169 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.169 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.170 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.170 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:21:09.170 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":86208,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:14.939 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:22:15.032 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.032 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.033 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.033 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.034 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.034 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.035 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.035 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.036 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.036 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.037 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.037 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.038 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.039 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.039 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.039 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.040 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.040 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.041 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:22:15.041 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":37156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:23:59.987 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:24:00.082 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.083 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.083 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.084 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.084 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.084 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.085 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.085 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.086 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.086 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.087 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.087 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.087 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.088 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.088 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.088 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.089 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.089 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.089 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:00.090 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74640,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.717 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:24:51.802 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.802 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.803 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.803 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.804 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.804 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.805 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.805 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.806 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.806 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.807 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.807 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.807 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.808 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.808 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.809 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.809 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.809 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.810 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:24:51.810 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":77512,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.788 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:27:08.879 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.880 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.880 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.881 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.881 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.881 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.882 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.882 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.882 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.883 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.883 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.884 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.885 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.885 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.886 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.886 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.887 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.887 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.888 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:27:08.888 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":74156,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.361 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:28:17.445 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.445 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.446 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.446 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.447 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.447 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.448 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.448 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.448 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.449 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.449 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.450 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.450 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.450 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.451 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.451 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.451 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.452 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.452 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:17.453 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":87828,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.246 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:28:48.342 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.342 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.342 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.343 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.343 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.344 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.344 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.344 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.345 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.345 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.346 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.346 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.346 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.347 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.347 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.348 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.348 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.348 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.349 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:28:48.349 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":54952,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.795 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:29:16.909 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.909 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.910 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.910 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.911 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.911 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.912 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.912 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.912 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.913 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.913 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.914 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.915 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.915 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.916 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.916 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.917 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.917 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.918 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:29:16.918 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56020,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.735 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:34:20.845 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.845 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.846 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.846 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.847 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.848 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.848 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.849 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.849 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.850 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.850 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.851 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.851 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.852 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.852 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.853 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.853 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.854 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.854 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:34:20.855 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":56484,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.374 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:35:05.487 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.487 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.488 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.489 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.489 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.489 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.490 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.490 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.491 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.491 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.492 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.492 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.493 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.495 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.496 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.496 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.497 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.497 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.498 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:35:05.498 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65264,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.832 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:40:18.932 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.932 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.932 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.933 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.933 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.934 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.934 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.935 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.935 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.935 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.936 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.936 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.936 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.937 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.937 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.938 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.938 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.938 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.939 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:40:18.939 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":46560,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:43:05.464 [ERROR] [Console] 应用启动失败: {} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":19412,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 16:46:32.398 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:46:32.545 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.546 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.547 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.547 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.548 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.548 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.549 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.549 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.550 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.550 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.551 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.551 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.552 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.552 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.553 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.553 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.554 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.554 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.555 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:46:32.556 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":65724,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.464 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:47:56.589 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.589 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.590 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.590 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.591 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.591 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.592 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.594 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.596 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.596 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.597 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.597 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.598 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.599 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.599 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.600 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.600 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.601 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.601 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:47:56.602 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":92644,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.335 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 16:48:31.445 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.445 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.446 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.446 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.447 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.447 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.448 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.448 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.448 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.449 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.449 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.450 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.450 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.451 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.451 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.452 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.452 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.453 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.453 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:48:31.454 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 16:57:40.200 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 16:57:40.201 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T08:57:40.201Z"}
2025-07-31 16:58:02.846 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 16:58:02.846 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T08:58:02.846Z"}
2025-07-31 16:59:26.307 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 16:59:26.307 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T08:59:26.307Z"}
2025-07-31 16:59:26.376 [ERROR] [GlobalExceptionFilter] GET /api/web/announcement/detail?id=27 - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 16:59:26.377 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/detail?id=27","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T08:59:26.377Z"}
2025-07-31 16:59:28.147 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 16:59:28.148 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T08:59:28.148Z"}
2025-07-31 17:02:06.690 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:02:17.177 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 17:02:17.177 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":91376,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T09:02:17.177Z"}
2025-07-31 17:06:00.180 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:06:00.285 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.286 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.286 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.287 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.287 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.288 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.288 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.288 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.289 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.289 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.290 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.290 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.291 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.291 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.291 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.292 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.292 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.293 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.293 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:00.294 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":27792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.662 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:06:51.756 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.756 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.756 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.757 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.757 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.757 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.758 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.758 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.758 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.759 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.759 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.759 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.760 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.760 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.760 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.761 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.761 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.761 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.762 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:51.762 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:06:52.267 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":71596,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.404 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:07:51.487 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.487 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.488 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.488 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.488 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.489 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.489 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.489 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.490 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.490 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.490 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.491 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.492 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.492 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.493 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.493 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.494 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.494 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.494 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:07:51.495 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":78792,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.575 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:08:24.699 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.700 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.700 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.701 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.701 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.702 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.702 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.702 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.703 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.703 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.704 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.704 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.704 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.705 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.705 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.706 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.706 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.706 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.707 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.707 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:10:11.288 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 17:10:11.289 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T09:10:11.289Z"}
2025-07-31 17:12:43.347 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":83072,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:12:58.920 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:12:59.031 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.031 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.032 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.033 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.033 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.034 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.034 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.034 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.035 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.035 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.036 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.036 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.036 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.037 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.037 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.038 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.038 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.039 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.039 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:12:59.039 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80420,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.850 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:14:50.956 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.956 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.957 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.958 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.958 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.959 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.959 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.960 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.961 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.961 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.962 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.962 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.962 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.963 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.963 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.964 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.964 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.965 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.965 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:14:50.966 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:15:18.657 [ERROR] [WebSocketGateway] 连接处理异常: 没有上传token {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:15:30.525 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 17:15:30.526 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":80620,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T09:15:30.525Z"}
2025-07-31 17:17:18.261 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:17:18.371 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.371 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.372 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.372 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.373 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.373 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.373 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.374 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.377 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.377 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.378 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.378 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.378 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.379 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.380 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.380 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.381 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.381 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.381 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:17:18.382 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":51768,"hostname":"DESKTOP-1L38AEG","stack":[null]}
