"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebUserInfoController = void 0;
const common_1 = require("@nestjs/common");
const web_user_info_service_1 = require("./web_user_info.service");
const swagger_1 = require("@nestjs/swagger");
const user_info_service_1 = require("../../util/database/mysql/user_info/user_info.service");
const user_password_reset_request_service_1 = require("../../util/database/mysql/user_password_reset_request/user_password_reset_request.service");
const not_login_decorator_1 = require("../router_guard/not-login.decorator");
const http_response_result_service_1 = require("../http_response_result/http_response_result.service");
const encrypt_decorator_1 = require("../../util/encrypt/encrypt.decorator");
let WebUserInfoController = class WebUserInfoController {
    webUserInfoService;
    userInfoService;
    passwordResetRequestService;
    httpResponseResultService;
    constructor(webUserInfoService, userInfoService, passwordResetRequestService, httpResponseResultService) {
        this.webUserInfoService = webUserInfoService;
        this.userInfoService = userInfoService;
        this.passwordResetRequestService = passwordResetRequestService;
        this.httpResponseResultService = httpResponseResultService;
    }
    async ping() {
        return this.httpResponseResultService.success({
            msg: 'pong',
            data: {
                timestamp: new Date().getTime(),
                status: 'ok'
            }
        });
    }
    async checkHasBindPhone(selectedStudents) {
        const result = await this.userInfoService.checkHasBindPhone(selectedStudents);
        return this.httpResponseResultService.success(result, '检查手机号绑定状态成功');
    }
    async getStudentInfo(id) {
        const userInfo = await this.userInfoService.findOne(id);
        let needSetPwd = false;
        if (userInfo.password === null || userInfo.password.trim() === "") {
            console.log("用户密码为空");
            needSetPwd = true;
        }
        if (!userInfo) {
            throw new common_1.NotFoundException('用户不存在');
        }
        return {
            ...userInfo,
            needSetPwd
        };
    }
    async updateUserInfo(updateData) {
        const result = await this.userInfoService.update(updateData.id, updateData);
        return {
            nickName: result.nickName,
            avatarUrl: result.avatarUrl,
            introduction: result.introduction
        };
    }
    async updatePassword(passwordData) {
        const userInfo = await this.userInfoService.findOne(passwordData.id);
        const isPasswordValid = this.webUserInfoService.validatePassword(userInfo.password, passwordData.oldPassword);
        if (!isPasswordValid) {
            throw new common_1.BadRequestException('旧密码验证失败');
        }
        const encryptedPassword = this.webUserInfoService.encryptPassword(passwordData.newPassword);
        await this.userInfoService.update(passwordData.id, {
            password: encryptedPassword
        });
        return {
            code: common_1.HttpStatus.OK,
            msg: '密码更新成功',
            data: null
        };
    }
    async requestPasswordReset(requestData) {
        const resetRequestData = this.webUserInfoService.preparePasswordResetRequestData(requestData);
        const request = await this.passwordResetRequestService.create(resetRequestData);
        return this.httpResponseResultService.success({
            msg: '密码重置申请已提交',
            data: {
                requestId: request.id
            }
        });
    }
    async handlePasswordReset(handleData) {
        const request = await this.passwordResetRequestService.findOne(handleData.requestId);
        const updatedRequest = this.webUserInfoService.preparePasswordResetHandleData(request, handleData);
        if (handleData.status === 2) {
            await this.passwordResetRequestService.update(updatedRequest.id, updatedRequest);
            return {
                code: common_1.HttpStatus.OK,
                msg: '已拒绝密码重置申请',
                data: null
            };
        }
        else if (handleData.status === 1) {
            try {
                const userInfo = await this.userInfoService.findByPhone(request.phone);
                if (!userInfo) {
                    return this.httpResponseResultService.error('用户不存在', null, 404);
                }
                const defaultPassword = this.webUserInfoService.getDefaultPassword();
                const encryptedPassword = this.webUserInfoService.encryptPassword(defaultPassword);
                await this.userInfoService.update(userInfo.id, {
                    password: encryptedPassword
                });
                await this.passwordResetRequestService.update(updatedRequest.id, updatedRequest);
                return {
                    code: common_1.HttpStatus.OK,
                    msg: `密码已重置为${defaultPassword}`,
                    data: null
                };
            }
            catch (error) {
                throw new common_1.BadRequestException(error.message || '重置密码失败');
            }
        }
        else {
            throw new common_1.BadRequestException('无效的处理状态');
        }
    }
    async getPasswordResetList(status, page = 1, size = 10) {
        const requests = await this.passwordResetRequestService.findAll();
        const result = this.webUserInfoService.paginateAndFilterRequests(requests, status, page, size);
        return {
            code: common_1.HttpStatus.OK,
            data: result
        };
    }
};
exports.WebUserInfoController = WebUserInfoController;
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '心跳检测', description: '用于客户端检测网络连接状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '服务器正常运行' }),
    (0, not_login_decorator_1.NotLogin)(),
    (0, common_1.Get)('/ping'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "ping", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '心跳检测', description: '用于客户端检测网络连接状态' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '服务器正常运行' }),
    (0, not_login_decorator_1.NotLogin)(),
    (0, common_1.Post)('/checkHasBindPhone'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "checkHasBindPhone", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取用户基础信息', description: '根据用户ID获取用户信息' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, common_1.Get)('/:id'),
    (0, encrypt_decorator_1.Encrypt)(),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "getStudentInfo", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新用户信息', description: '更新用户的昵称、头像和简介' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', description: '用户ID' },
                nickName: { type: 'string', description: '用户昵称' },
                avatarUrl: { type: 'string', description: '用户头像URL' },
                introduction: { type: 'string', description: '用户简介' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '更新成功' }),
    (0, common_1.Put)('/update'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "updateUserInfo", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '更新用户密码', description: '验证旧密码并更新为新密码' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                id: { type: 'number', description: '用户ID' },
                oldPassword: { type: 'string', description: '旧密码' },
                newPassword: { type: 'string', description: '新密码' }
            },
            required: ['id', 'oldPassword', 'newPassword']
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '密码更新成功' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: '旧密码验证失败' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '用户不存在' }),
    (0, encrypt_decorator_1.SecureEncrypt)({
        enabled: true,
        decryptRequest: true
    }),
    (0, common_1.Put)('/update/password'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "updatePassword", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '提交密码重置申请', description: '用户忘记密码时提交重置申请' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                phone: { type: 'string', description: '用户手机号' },
                name: { type: 'string', description: '用户姓名' },
                contactInfo: { type: 'string', description: '联系方式' },
                remark: { type: 'string', description: '备注信息' },
                schoolName: { type: 'string', description: '学校名称(针对学生用户)' },
                studentNumber: { type: 'string', description: '学号(针对学生用户)' }
            },
            required: ['phone', 'name']
        }
    }),
    (0, not_login_decorator_1.NotLogin)(),
    (0, swagger_1.ApiResponse)({ status: 200, description: '申请提交成功' }),
    (0, common_1.Post)('reset_password/request'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "requestPasswordReset", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '处理密码重置申请', description: '管理员处理用户的密码重置申请' }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            properties: {
                requestId: { type: 'number', description: '密码重置申请ID' },
                status: { type: 'number', description: '处理状态：1-同意，2-拒绝' },
                handleRemark: { type: 'string', description: '处理备注' },
                handlerId: { type: 'number', description: '处理人ID' }
            },
            required: ['requestId', 'status', 'handlerId']
        }
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '处理成功' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: '申请不存在' }),
    (0, common_1.Put)('/reset/password/handle'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "handlePasswordReset", null);
__decorate([
    (0, swagger_1.ApiOperation)({ summary: '获取密码重置申请列表', description: '管理员获取密码重置申请列表' }),
    (0, swagger_1.ApiQuery)({ name: 'status', description: '状态过滤：0-待处理，1-已同意，2-已拒绝', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'page', description: '页码', required: false }),
    (0, swagger_1.ApiQuery)({ name: 'size', description: '每页数量', required: false }),
    (0, swagger_1.ApiResponse)({ status: 200, description: '获取成功' }),
    (0, common_1.Get)('/reset/password/list'),
    __param(0, (0, common_1.Query)('status')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('size')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], WebUserInfoController.prototype, "getPasswordResetList", null);
exports.WebUserInfoController = WebUserInfoController = __decorate([
    (0, swagger_1.ApiTags)('api/web/用户信息(web/user/info)'),
    (0, common_1.Controller)('api/web/user/info'),
    __metadata("design:paramtypes", [web_user_info_service_1.WebUserInfoService,
        user_info_service_1.UserInfoService,
        user_password_reset_request_service_1.UserPasswordResetRequestService,
        http_response_result_service_1.HttpResponseResultService])
], WebUserInfoController);
//# sourceMappingURL=web_user_info.controller.js.map