2025-07-31 17:08:20.976 [INFO] [Business] Business IpLocationUtil.initialize {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","module":"IpLocationUtil","action":"initialize","data":"{\"message\":\"ip2region初始化成功\"}"}
2025-07-31 17:08:21.607 [INFO] [Startup] Application is starting... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","nodeVersion":"v20.17.0","platform":"win32","port":8003}
2025-07-31 17:08:23.732 [INFO] [ConsoleOverride] Console methods have been overridden to use <PERSON> logger {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.770 [INFO] [Console] Redis连接成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.776 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.830 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.832 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.834 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.835 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.836 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.838 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.838 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.839 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.841 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.842 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.843 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.845 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.846 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.847 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.849 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.853 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.855 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.857 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.858 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.860 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.861 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.862 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.864 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.865 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.866 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.867 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.869 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.871 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.872 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.874 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.876 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.877 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.879 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.881 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.883 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.884 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.886 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.888 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.889 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.890 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.893 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.894 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.895 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.896 [WARN] [Console] It is highly recommended to use a minimum Redis version of 6.2.0
             Current: ******** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.915 [INFO] [Console] 🚀 开始初始化 LoginLoggerUtil... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.916 [INFO] [Console] 🔍 服务实例信息: {
  "hasService": true,
  "serviceType": "UserLoginLogService"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.917 [INFO] [Console] 🔧 设置 LoginLoggerUtil 服务实例: {
  "hasService": true,
  "serviceType": "UserLoginLogService"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.918 [INFO] [Console] ✅ LoginLoggerUtil 服务实例设置完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.918 [INFO] [Console] ✅ LoginLoggerUtil 初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:23.926 [INFO] [Ip2RegionService] IP2Region服务初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.249 [INFO] [AppModule] App模块初始化... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.250 [INFO] [AppModule] 通过App模块手动触发模板更新... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.544 [INFO] [NestFactory] Starting Nest application... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.544 [INFO] [PaymentTemplateService] 已加载模板: payment_fail {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.545 [INFO] [PaymentTemplateService] 已加载模板: payment_success {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.545 [INFO] [PaymentTemplateService] 已加载模板: refund_fail {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.546 [INFO] [PaymentTemplateService] 已加载模板: refund_success {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.546 [INFO] [PaymentTemplateService] 已加载 4 个模板 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.547 [INFO] [InstanceLoader] WebModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.547 [INFO] [InstanceLoader] YamlModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.548 [INFO] [InstanceLoader] AliServiceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.548 [INFO] [InstanceLoader] ScratchModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.549 [INFO] [InstanceLoader] UtilModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.549 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.549 [INFO] [InstanceLoader] WebWeixinScanModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.550 [INFO] [InstanceLoader] UserCommonLocationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.550 [INFO] [InstanceLoader] CommonServicesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.551 [INFO] [InstanceLoader] WinstonModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.552 [INFO] [InstanceLoader] ConfigHostModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.552 [INFO] [InstanceLoader] WebTemplateFolderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.553 [INFO] [InstanceLoader] ZwwModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.553 [INFO] [InstanceLoader] DiscoveryModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.554 [INFO] [InstanceLoader] LoggerModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.554 [INFO] [InstanceLoader] ScratchConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.555 [INFO] [InstanceLoader] AiProvidersConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.555 [INFO] [InstanceLoader] JwtModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.555 [INFO] [InstanceLoader] AliConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.556 [INFO] [InstanceLoader] DatabaseConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.556 [INFO] [XunfeiSpeechRecognitionService] 初始化讯飞语音识别服务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.557 [INFO] [XunfeiSpeechRecognitionService] AppID: 0292**** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.557 [INFO] [XunfeiSpeechRecognitionService] WebSocket URL: ws://iat.xf-yun.com/v1 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.558 [INFO] [XunfeiVoiceprintRecognitionService] 初始化讯飞声纹识别服务 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.559 [INFO] [XunfeiVoiceprintRecognitionService] AppID: 4f01**** {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.559 [INFO] [XunfeiVoiceprintRecognitionService] API URL: https://api.xf-yun.com/v1/private/s782b4996 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.560 [INFO] [QueueService] 队列服务初始化完成，配置10分钟过期时间 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.561 [INFO] [InstanceLoader] ConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.561 [INFO] [InstanceLoader] ConfigModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.562 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.562 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.563 [INFO] [InstanceLoader] ScheduleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.563 [INFO] [RedisSessionService] Redis会话存储服务已初始化 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.564 [INFO] [InstanceLoader] AliGreenModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.564 [INFO] [InstanceLoader] BullModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.564 [INFO] [InstanceLoader] MinimaxImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.565 [INFO] [InstanceLoader] ZhipuLlmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.565 [INFO] [InstanceLoader] BaiduImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.566 [INFO] [InstanceLoader] AliyunImageScoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.566 [INFO] [InstanceLoader] AliyunSegmentImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.566 [INFO] [InstanceLoader] MinimaxTtsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.567 [INFO] [InstanceLoader] AliSmsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.567 [INFO] [AlipayStrategy] 初始化支付宝支付策略... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.568 [DEBUG] [AlipayStrategy] 支付宝配置: appId=2021005170688349, gateway=https://openapi.alipay.com/gateway.do {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.568 [INFO] [AlipayStrategy] 支付宝支付策略初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.568 [INFO] [WechatPayStrategy] 微信支付客户端初始化成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.569 [INFO] [InstanceLoader] RedisModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.569 [INFO] [InstanceLoader] AliQwenVisionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.569 [INFO] [InstanceLoader] AliyunExpressionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.570 [INFO] [InstanceLoader] AliyunFaceCompareModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.570 [INFO] [InstanceLoader] AliyunFaceRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.571 [INFO] [InstanceLoader] AliyunObjectDetectionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.571 [INFO] [InstanceLoader] XunfeiVoiceprintRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.571 [INFO] [InstanceLoader] SessionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.572 [INFO] [InstanceLoader] KeyManagementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.572 [INFO] [InstanceLoader] AliyunStaticGestureRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.573 [INFO] [InstanceLoader] AliQwenTurboModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.573 [INFO] [EncryptionService] 【初始化】加密服务开始初始化... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.574 [WARN] [EncryptionService] 【初始化】获取RSA密钥对失败: RSA密钥对未初始化，请稍后重试，服务将在首次请求时重试 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.574 [INFO] [EncryptionService] 【初始化】加密服务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.575 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (F:\\logicleap2\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (F:\\logicleap2\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (F:\\logicleap2\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 17:08:24.576 [INFO] [KeyManagementService] 生成了新的RSA密钥对，ID: key-0c386bf5，指纹: 0c386bf5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.577 [INFO] [InstanceLoader] WebSocketModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.577 [INFO] [InstanceLoader] HttpResponseResultModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.577 [INFO] [InstanceLoader] QueueModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.578 [INFO] [EncryptionCleanupTask] 【初始化】会话清理任务已启动 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.579 [INFO] [InstanceLoader] OssModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.579 [INFO] [InstanceLoader] XunfeiSpeechRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.580 [INFO] [InstanceLoader] MysqlModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.580 [INFO] [InstanceLoader] RouterGuardModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.580 [INFO] [InstanceLoader] AliOssModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.581 [INFO] [InstanceLoader] EncryptModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.581 [INFO] [InstanceLoader] TypeOrmCoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.582 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.582 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.582 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.583 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.583 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.583 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.584 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.584 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.584 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.585 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.585 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.585 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.586 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.586 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.587 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.587 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.587 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.588 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.588 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.588 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.589 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.589 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.590 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.590 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.590 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.591 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.591 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.592 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.592 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.592 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.593 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.593 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.593 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.594 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.594 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.594 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.595 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.595 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.596 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.597 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.601 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.602 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.603 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.603 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.604 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.604 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.604 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.605 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.605 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.605 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.606 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.606 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.607 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.607 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.607 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.608 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.608 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.609 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.609 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.609 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.610 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.610 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.610 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.611 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.612 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.612 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.613 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.613 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.614 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.614 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.615 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.615 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.615 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.616 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.616 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.616 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.617 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.617 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.617 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.618 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.618 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.619 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.619 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.619 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.620 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.620 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.621 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.621 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.621 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.622 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.622 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.622 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.623 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.623 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.624 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.624 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.625 [INFO] [InstanceLoader] TypeOrmModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.625 [INFO] [InstanceLoader] TeacherAuditAttachmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.625 [INFO] [InstanceLoader] UserSrchImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.626 [INFO] [InstanceLoader] UserSrchEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.626 [INFO] [InstanceLoader] UserSrchAudioModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.627 [INFO] [InstanceLoader] UserSrchImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.627 [INFO] [InstanceLoader] TrainImageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.627 [INFO] [InstanceLoader] TrainPoseModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.628 [INFO] [InstanceLoader] TrainSoundModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.628 [INFO] [InstanceLoader] WebWorkAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.628 [INFO] [InstanceLoader] UserRoleRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.629 [INFO] [InstanceLoader] UserJoinRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.629 [INFO] [InstanceLoader] TeacherTaskAssignmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.630 [INFO] [InstanceLoader] RolePermissionTemplatesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.632 [INFO] [InstanceLoader] UserPointsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.633 [INFO] [InstanceLoader] UserPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.634 [INFO] [InstanceLoader] PackageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.634 [INFO] [InstanceLoader] UserPointsPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.635 [INFO] [InstanceLoader] UserClassModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.636 [INFO] [InstanceLoader] UserSchoolModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.636 [INFO] [InstanceLoader] UserStudentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.637 [INFO] [InstanceLoader] KeyPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.637 [INFO] [InstanceLoader] UserRoleRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.639 [INFO] [InstanceLoader] UserJoinRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.640 [INFO] [InstanceLoader] RoleTemplateExtensionPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.641 [INFO] [InstanceLoader] RoleTemplateBlockPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.641 [INFO] [InstanceLoader] RoleTemplateFolderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.642 [INFO] [InstanceLoader] RoleTemplateFolderJoinTemplateModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.642 [INFO] [InstanceLoader] TableJoingModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.646 [INFO] [InstanceLoader] UserWorkInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.647 [INFO] [InstanceLoader] WorkModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.647 [INFO] [InstanceLoader] TeacherTaskAssignmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.647 [INFO] [InstanceLoader] TeacherTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.648 [INFO] [InstanceLoader] UserImageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.648 [INFO] [InstanceLoader] TaskSelfAssessmentItemModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.649 [INFO] [InstanceLoader] UserPasswordResetRequestModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.650 [INFO] [InstanceLoader] UserImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.650 [INFO] [InstanceLoader] UserVoiceInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.650 [INFO] [InstanceLoader] UserSchoolRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.651 [INFO] [InstanceLoader] AnnouncementReadRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.651 [INFO] [InstanceLoader] AnnouncementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.651 [INFO] [InstanceLoader] AnnouncementAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.652 [INFO] [InstanceLoader] SpaceCarouselMapModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.652 [INFO] [InstanceLoader] CarouselAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.652 [INFO] [InstanceLoader] WebRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.653 [INFO] [InstanceLoader] BlockModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.653 [INFO] [InstanceLoader] ExtensionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.653 [INFO] [InstanceLoader] ExtensionPermissionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.654 [INFO] [InstanceLoader] BlockPermissionsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.654 [INFO] [InstanceLoader] UserReportModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.654 [INFO] [InstanceLoader] DocModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.655 [INFO] [InstanceLoader] KeyPackageRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.655 [INFO] [InstanceLoader] UserImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.655 [INFO] [InstanceLoader] AttachmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.656 [INFO] [InstanceLoader] SelfAssessmentItemModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.656 [INFO] [InstanceLoader] PackageOrderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.656 [INFO] [InstanceLoader] PackagePricingModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.657 [INFO] [InstanceLoader] PaymentRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.657 [INFO] [InstanceLoader] NotificationRecordModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.657 [INFO] [InstanceLoader] PaymentLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.658 [INFO] [InstanceLoader] PaymentOrderModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.658 [INFO] [InstanceLoader] PaymentRefundModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.658 [INFO] [InstanceLoader] UserLoginLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.659 [INFO] [InstanceLoader] TagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.659 [INFO] [InstanceLoader] ActivityModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.659 [INFO] [InstanceLoader] ActivityTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.660 [INFO] [InstanceLoader] ActivityWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.660 [INFO] [InstanceLoader] ImageTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.660 [INFO] [InstanceLoader] PoseTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.660 [INFO] [InstanceLoader] AudioTrainModelModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.661 [INFO] [InstanceLoader] UserPointsOfflineMessageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.661 [INFO] [InstanceLoader] UserRolePermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.661 [INFO] [InstanceLoader] UserRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.662 [INFO] [InstanceLoader] UserSchoolRelationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.662 [INFO] [InstanceLoader] UserWorkLikeModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.662 [INFO] [InstanceLoader] ActivityAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.663 [INFO] [InstanceLoader] ParticipationAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.663 [INFO] [InstanceLoader] WorkAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.663 [INFO] [InstanceLoader] StudentSelfAssessmentSubmissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.664 [INFO] [InstanceLoader] VoiceprintGroupModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.664 [INFO] [InstanceLoader] VoiceprintFeatureModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.664 [INFO] [InstanceLoader] UserRoleModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.665 [INFO] [InstanceLoader] UserLoginLogModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.665 [INFO] [InstanceLoader] WebUserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.665 [INFO] [InstanceLoader] UserSrchWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.665 [INFO] [InstanceLoader] UserSrchTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.666 [INFO] [InstanceLoader] UserSchoolModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.666 [INFO] [InstanceLoader] UserImageInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.666 [INFO] [InstanceLoader] UserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.666 [INFO] [InstanceLoader] AppModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.667 [INFO] [InstanceLoader] TeacherAuditModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.667 [INFO] [InstanceLoader] WebPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.667 [INFO] [InstanceLoader] ActivitySubmitModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.668 [INFO] [InstanceLoader] ActivityEventsTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.668 [INFO] [InstanceLoader] UserPointPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.668 [INFO] [InstanceLoader] UserStudentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.668 [INFO] [InstanceLoader] WeixinModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.669 [INFO] [InstanceLoader] WebAnnouncementModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.669 [INFO] [InstanceLoader] WebCarouselModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.669 [INFO] [InstanceLoader] WebDocModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.669 [INFO] [InstanceLoader] WebKeyPackageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.670 [INFO] [InstanceLoader] UserClassModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.670 [INFO] [InstanceLoader] UserTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.670 [INFO] [InstanceLoader] WebActivityTagModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.671 [INFO] [InstanceLoader] WebActivityWorkModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.671 [INFO] [InstanceLoader] UserWorkLikeModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.671 [INFO] [InstanceLoader] TPSModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.671 [INFO] [InstanceLoader] UserWorkInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.672 [INFO] [InstanceLoader] TeacherTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.672 [INFO] [InstanceLoader] WebPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.673 [INFO] [QueueService] ✅ 创建队列: vision-qwen {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.673 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.673 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.673 [INFO] [QueueService] ✅ 创建队列: image-enhance {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.674 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.674 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.674 [INFO] [QueueService] ✅ 创建队列: image-score {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.675 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.675 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.676 [INFO] [QueueService] ✅ 创建队列: voiceprint-recognition {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.676 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.676 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.677 [INFO] [QueueService] ✅ 创建队列: static-gesture-recognition {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.677 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.678 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.678 [INFO] [QueueService] ✅ 创建队列: text-glm {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.679 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.679 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.679 [INFO] [QueueService] ✅ 创建队列: text-qwen {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.680 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.680 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.681 [INFO] [QueueService] ✅ 创建队列: expression-recognition {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.681 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.681 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.682 [INFO] [QueueService] ✅ 创建队列: face-compare {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.682 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.682 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.683 [INFO] [QueueService] ✅ 创建队列: face-recognition {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.683 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.683 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.684 [INFO] [QueueService] ✅ 创建队列: speech-synthesis {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.684 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.684 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.685 [INFO] [QueueService] ✅ 创建队列: speech-recognition {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.685 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.686 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.686 [INFO] [QueueService] ✅ 创建队列: image-object {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.686 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.687 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.687 [INFO] [QueueService] ✅ 创建队列: image-generate {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.687 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.688 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.688 [INFO] [QueueService] ✅ 创建队列: image-segment {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.688 [INFO] [QueueService] ⏰ TTL: 600000ms (10分钟) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.689 [INFO] [QueueService] 🧹 清理配置 - 完成任务: 10个, 失败任务: 5个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.689 [INFO] [InstanceLoader] UserInfoModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.689 [INFO] [InstanceLoader] UserSrchTemplatesModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.690 [INFO] [InstanceLoader] IpLocationModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.690 [INFO] [InstanceLoader] WebReportModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.691 [INFO] [InstanceLoader] UserPasswordResetModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.691 [INFO] [InstanceLoader] WebWeixinScanModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.691 [INFO] [InstanceLoader] WeixinUtilsModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.692 [INFO] [InstanceLoader] PackageOrderBusinessModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.692 [INFO] [InstanceLoader] UserPointModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.692 [INFO] [InstanceLoader] WebActivityModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.693 [INFO] [InstanceLoader] UserAuthModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.693 [INFO] [InstanceLoader] WebEventsTaskModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.693 [INFO] [InstanceLoader] WeixinMessageModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.694 [INFO] [InstanceLoader] WebPointPermissionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.694 [INFO] [InstanceLoader] WebPointModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.694 [INFO] [InstanceLoader] PaymentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.695 [INFO] [InstanceLoader] CourseModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.695 [INFO] [InstanceLoader] AiVisualRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.695 [INFO] [InstanceLoader] AiImageScoreModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.695 [INFO] [InstanceLoader] AiStaticGestureRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.696 [INFO] [InstanceLoader] AiExpressionRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.696 [INFO] [InstanceLoader] AiFaceCompareModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.697 [INFO] [InstanceLoader] AiFaceRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.697 [INFO] [InstanceLoader] AiSpeechRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.697 [INFO] [InstanceLoader] AiObjectDetectionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.698 [INFO] [InstanceLoader] AiTextDialogueModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.698 [INFO] [InstanceLoader] AiImageEnhanceModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.698 [INFO] [InstanceLoader] AiVoiceprintRecognitionModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.698 [INFO] [InstanceLoader] AiSpeechSynthesisModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.699 [INFO] [InstanceLoader] AiImageGenerateModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.699 [INFO] [InstanceLoader] AiImageSegmentModule dependencies initialized {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.700 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.700 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.700 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.701 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.701 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.702 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.702 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.702 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.703 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.703 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.704 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.704 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.704 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.705 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.705 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.706 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.706 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.707 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.707 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.707 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","stack":[null]}
2025-07-31 17:08:24.708 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "add_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.708 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "check_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.708 [INFO] [WebSocketsController] WebSocketGateway subscribed to the "message_to_tab" message {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.709 [INFO] [RoutesResolver] AppController {/}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.709 [INFO] [RouterExplorer] Mapped {/, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.710 [INFO] [RoutesResolver] WorkAuditController {/api/work-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.710 [INFO] [RouterExplorer] Mapped {/api/work-audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.710 [INFO] [RouterExplorer] Mapped {/api/work-audit/listByWork/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.711 [INFO] [RouterExplorer] Mapped {/api/work-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.711 [INFO] [RouterExplorer] Mapped {/api/work-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.711 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.712 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.712 [INFO] [RouterExplorer] Mapped {/api/work-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.712 [INFO] [RoutesResolver] UserRoleRelationController {/api/user-role-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.712 [INFO] [RouterExplorer] Mapped {/api/user-role-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.713 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/batch-assign-roles, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.713 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/batch-assign-users, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.713 [INFO] [RouterExplorer] Mapped {/api/user-role-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.714 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.714 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.714 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.715 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.715 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.715 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.716 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.716 [INFO] [RouterExplorer] Mapped {/api/user-role-relation/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.716 [INFO] [RoutesResolver] UserJoinRoleController {/api/user-join-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.717 [INFO] [RouterExplorer] Mapped {/api/user-join-role/createUserJoinRole, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.717 [INFO] [RouterExplorer] Mapped {/api/user-join-role/batchCreateUserJoinRole, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.717 [INFO] [RouterExplorer] Mapped {/api/user-join-role/getUserRoleAndTemplateId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.718 [INFO] [RouterExplorer] Mapped {/api/user-join-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.718 [INFO] [RouterExplorer] Mapped {/api/user-join-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.719 [INFO] [RouterExplorer] Mapped {/api/user-join-role/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.719 [INFO] [RouterExplorer] Mapped {/api/user-join-role/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.719 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.720 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.720 [INFO] [RouterExplorer] Mapped {/api/user-join-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.720 [INFO] [RouterExplorer] Mapped {/api/user-join-role/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.721 [INFO] [RoutesResolver] TeacherTaskAssignmentController {/api/teacher-task-assignment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.721 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/mark-read, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.722 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/return-revision, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.722 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/studentSubmitedWork, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.722 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/publish-to-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.723 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/grade, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.723 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/studentCommitWork, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.723 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/submitWork, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.724 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/workSubmissionsRecord, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.724 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.724 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.725 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.725 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.726 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.726 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.726 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.727 [INFO] [RouterExplorer] Mapped {/api/teacher-task-assignment/update-status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.727 [INFO] [RoutesResolver] UserWorkInfoController {/api/user-work-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.727 [INFO] [RouterExplorer] Mapped {/api/user-work-info/reviewList, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.728 [INFO] [RouterExplorer] Mapped {/api/user-work-info/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.728 [INFO] [RouterExplorer] Mapped {/api/user-work-info/searchClassprojects, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.728 [INFO] [RouterExplorer] Mapped {/api/user-work-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.729 [INFO] [RouterExplorer] Mapped {/api/user-work-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.729 [INFO] [RouterExplorer] Mapped {/api/user-work-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.729 [INFO] [RouterExplorer] Mapped {/api/user-work-info/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.729 [INFO] [RouterExplorer] Mapped {/api/user-work-info/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.730 [INFO] [RouterExplorer] Mapped {/api/user-work-info/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.730 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.730 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.731 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.737 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.739 [INFO] [RouterExplorer] Mapped {/api/user-work-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.742 [INFO] [RouterExplorer] Mapped {/api/user-work-info/like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.742 [INFO] [RouterExplorer] Mapped {/api/user-work-info/class/projects, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.743 [INFO] [RoutesResolver] UserWorkLikeController {/api/user-work-like}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.744 [INFO] [RouterExplorer] Mapped {/api/user-work-like/likeList, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.744 [INFO] [RouterExplorer] Mapped {/api/user-work-like, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.745 [INFO] [RouterExplorer] Mapped {/api/user-work-like, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.747 [INFO] [RouterExplorer] Mapped {/api/user-work-like/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.747 [INFO] [RouterExplorer] Mapped {/api/user-work-like/target/:targetId/type/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.748 [INFO] [RouterExplorer] Mapped {/api/user-work-like/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.748 [INFO] [RouterExplorer] Mapped {/api/user-work-like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.749 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.749 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.750 [INFO] [RouterExplorer] Mapped {/api/user-work-like/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.750 [INFO] [RoutesResolver] UserImageInfoController {/api/user-image-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.750 [INFO] [RouterExplorer] Mapped {/api/user-image-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.751 [INFO] [RouterExplorer] Mapped {/api/user-image-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.751 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.751 [INFO] [RouterExplorer] Mapped {/api/user-image-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.752 [INFO] [RouterExplorer] Mapped {/api/user-image-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.752 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.753 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.753 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.754 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.754 [INFO] [RouterExplorer] Mapped {/api/user-image-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.754 [INFO] [RouterExplorer] Mapped {/api/user-image-info/like/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.755 [INFO] [RoutesResolver] UserInfoController {/user-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.755 [INFO] [RouterExplorer] Mapped {/user-info/condition/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.755 [INFO] [RouterExplorer] Mapped {/user-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.756 [INFO] [RouterExplorer] Mapped {/user-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.756 [INFO] [RouterExplorer] Mapped {/user-info/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.756 [INFO] [RouterExplorer] Mapped {/user-info/phone/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.757 [INFO] [RouterExplorer] Mapped {/user-info/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.757 [INFO] [RouterExplorer] Mapped {/user-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.758 [INFO] [RouterExplorer] Mapped {/user-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.759 [INFO] [RouterExplorer] Mapped {/user-info/:id/points/:points, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.759 [INFO] [RouterExplorer] Mapped {/user-info/:id/role/:roleId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.759 [INFO] [RouterExplorer] Mapped {/user-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.760 [INFO] [RoutesResolver] RolePermissionTemplatesController {/role-permission-templates}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.760 [INFO] [RouterExplorer] Mapped {/role-permission-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.761 [INFO] [RouterExplorer] Mapped {/role-permission-templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.761 [INFO] [RouterExplorer] Mapped {/role-permission-templates/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.761 [INFO] [RouterExplorer] Mapped {/role-permission-templates/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.762 [INFO] [RouterExplorer] Mapped {/role-permission-templates/role/:roleId/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.762 [INFO] [RouterExplorer] Mapped {/role-permission-templates/official, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.762 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.763 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.763 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.763 [INFO] [RouterExplorer] Mapped {/role-permission-templates/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.764 [INFO] [RoutesResolver] UserStudentController {/api/user-student}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.764 [INFO] [RouterExplorer] Mapped {/api/user-student/export, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.765 [INFO] [RouterExplorer] Mapped {/api/user-student/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.765 [INFO] [RouterExplorer] Mapped {/api/user-student/match-by-ids, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.766 [INFO] [RouterExplorer] Mapped {/api/user-student/batch-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.766 [INFO] [RouterExplorer] Mapped {/api/user-student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.766 [INFO] [RouterExplorer] Mapped {/api/user-student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.767 [INFO] [RouterExplorer] Mapped {/api/user-student/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.767 [INFO] [RouterExplorer] Mapped {/api/user-student/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.767 [INFO] [RouterExplorer] Mapped {/api/user-student/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.768 [INFO] [RouterExplorer] Mapped {/api/user-student/school/:schoolId/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.768 [INFO] [RouterExplorer] Mapped {/api/user-student/number, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.768 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.769 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.769 [INFO] [RouterExplorer] Mapped {/api/user-student/:id/class/:classId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.770 [INFO] [RouterExplorer] Mapped {/api/user-student/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.770 [INFO] [RouterExplorer] Mapped {/api/user-student/user/:userId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.771 [INFO] [RoutesResolver] UserPointController {/api/user-point}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.772 [INFO] [RouterExplorer] Mapped {/api/user-point/total, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.772 [INFO] [RouterExplorer] Mapped {/api/user-point/:userId/total, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.773 [INFO] [RouterExplorer] Mapped {/api/user-point/details, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.773 [INFO] [RouterExplorer] Mapped {/api/user-point/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.773 [INFO] [RouterExplorer] Mapped {/api/user-point/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.774 [INFO] [RouterExplorer] Mapped {/api/user-point/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.775 [INFO] [RouterExplorer] Mapped {/api/user-point/assignPoints, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.775 [INFO] [RouterExplorer] Mapped {/api/user-point/batch-total, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.775 [INFO] [RouterExplorer] Mapped {/api/user-point/student/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.776 [INFO] [RouterExplorer] Mapped {/api/user-point/student/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.777 [INFO] [RouterExplorer] Mapped {/api/user-point/teacher/student/:studentId/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.777 [INFO] [RouterExplorer] Mapped {/api/user-point/packages, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.778 [INFO] [RoutesResolver] UserPointPackageController {/user-point-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.778 [INFO] [RouterExplorer] Mapped {/user-point-package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.778 [INFO] [RouterExplorer] Mapped {/user-point-package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.779 [INFO] [RouterExplorer] Mapped {/user-point-package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.779 [INFO] [RouterExplorer] Mapped {/user-point-package/clean-expired, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.779 [INFO] [RouterExplorer] Mapped {/user-point-package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.780 [INFO] [RoutesResolver] UserPointsController {/user-points}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.780 [INFO] [RouterExplorer] Mapped {/user-points, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.780 [INFO] [RouterExplorer] Mapped {/user-points, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.781 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.781 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/date-range, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.781 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.782 [INFO] [RouterExplorer] Mapped {/user-points/user/:userId/source/:source, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.782 [INFO] [RouterExplorer] Mapped {/user-points/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.782 [INFO] [RouterExplorer] Mapped {/user-points/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.782 [INFO] [RouterExplorer] Mapped {/user-points/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.783 [INFO] [RoutesResolver] HttpResponseResultController {/http-response}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.783 [INFO] [RouterExplorer] Mapped {/http-response/success, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.783 [INFO] [RouterExplorer] Mapped {/http-response/error, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.784 [INFO] [RouterExplorer] Mapped {/http-response/custom, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.784 [INFO] [RoutesResolver] UserPackageController {/user-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.784 [INFO] [RouterExplorer] Mapped {/user-package, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.784 [INFO] [RouterExplorer] Mapped {/user-package, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.784 [INFO] [RouterExplorer] Mapped {/user-package/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.785 [INFO] [RouterExplorer] Mapped {/user-package/user/:userId/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.785 [INFO] [RouterExplorer] Mapped {/user-package/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.785 [INFO] [RouterExplorer] Mapped {/user-package/check-expired, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.786 [INFO] [RouterExplorer] Mapped {/user-package/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.786 [INFO] [RouterExplorer] Mapped {/user-package/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.790 [INFO] [RouterExplorer] Mapped {/user-package/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.790 [INFO] [RoutesResolver] PackageInfoController {/package-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.791 [INFO] [RouterExplorer] Mapped {/package-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.791 [INFO] [RouterExplorer] Mapped {/package-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.792 [INFO] [RouterExplorer] Mapped {/package-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.792 [INFO] [RouterExplorer] Mapped {/package-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.792 [INFO] [RouterExplorer] Mapped {/package-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.793 [INFO] [RoutesResolver] UserPointsPermissionController {/user-points-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.793 [INFO] [RouterExplorer] Mapped {/user-points-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.794 [INFO] [RouterExplorer] Mapped {/user-points-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.794 [INFO] [RouterExplorer] Mapped {/user-points-permission/student/:studentUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.795 [INFO] [RouterExplorer] Mapped {/user-points-permission/teacher/:teacherUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.796 [INFO] [RouterExplorer] Mapped {/user-points-permission/valid, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.797 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.797 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.797 [INFO] [RouterExplorer] Mapped {/user-points-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.797 [INFO] [RoutesResolver] UserClassController {/api/user/class}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.798 [INFO] [RouterExplorer] Mapped {/api/user/class, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.798 [INFO] [RouterExplorer] Mapped {/api/user/class/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.798 [INFO] [RouterExplorer] Mapped {/api/user/class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.799 [INFO] [RouterExplorer] Mapped {/api/user/class/update/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.799 [INFO] [RouterExplorer] Mapped {/api/user/class/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.799 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.800 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/:teacherId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.800 [INFO] [RouterExplorer] Mapped {/api/user/class/assistant/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.800 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.801 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/export, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.801 [INFO] [RouterExplorer] Mapped {/api/user/class/:id/invite, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.801 [INFO] [RouterExplorer] Mapped {/api/user/class/student/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.802 [INFO] [RouterExplorer] Mapped {/api/user/class/invite_join, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.802 [INFO] [RouterExplorer] Mapped {/api/user/class/transfer, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.802 [INFO] [RouterExplorer] Mapped {/api/user/class/import, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.803 [INFO] [RouterExplorer] Mapped {/api/user/class/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.803 [INFO] [RouterExplorer] Mapped {/api/user/class/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.803 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/classes/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.803 [INFO] [RouterExplorer] Mapped {/api/user/class/student/remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.804 [INFO] [RouterExplorer] Mapped {/api/user/class/assistant/remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.804 [INFO] [RouterExplorer] Mapped {/api/user/class/teacher/search/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.804 [INFO] [RouterExplorer] Mapped {/api/user/class/school/:schoolId/all, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.804 [INFO] [RoutesResolver] UserClassController {/user-class}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.805 [INFO] [RouterExplorer] Mapped {/user-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.805 [INFO] [RouterExplorer] Mapped {/user-class, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.805 [INFO] [RouterExplorer] Mapped {/user-class/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.805 [INFO] [RouterExplorer] Mapped {/user-class/school/:schoolId/grade/:grade, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.806 [INFO] [RouterExplorer] Mapped {/user-class/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.806 [INFO] [RouterExplorer] Mapped {/user-class/assistant-teacher/:assistantTeacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.806 [INFO] [RouterExplorer] Mapped {/user-class/invite-code/:inviteCode, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.807 [INFO] [RouterExplorer] Mapped {/user-class/:id/regenerate-invite-code, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.807 [INFO] [RouterExplorer] Mapped {/user-class/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.807 [INFO] [RouterExplorer] Mapped {/user-class/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.807 [INFO] [RouterExplorer] Mapped {/user-class/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.808 [INFO] [RoutesResolver] UserSchoolController {/user-school}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.808 [INFO] [RouterExplorer] Mapped {/user-school, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.808 [INFO] [RouterExplorer] Mapped {/user-school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.808 [INFO] [RouterExplorer] Mapped {/user-school/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.809 [INFO] [RouterExplorer] Mapped {/user-school/area, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.809 [INFO] [RouterExplorer] Mapped {/user-school/provinces, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.809 [INFO] [RouterExplorer] Mapped {/user-school/cities/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.809 [INFO] [RouterExplorer] Mapped {/user-school/districts/:province/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.809 [INFO] [RouterExplorer] Mapped {/user-school/province/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.810 [INFO] [RouterExplorer] Mapped {/user-school/province/:province/city/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.810 [INFO] [RouterExplorer] Mapped {/user-school/province/:province/city/:city/district/:district, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.810 [INFO] [RouterExplorer] Mapped {/user-school/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.811 [INFO] [RouterExplorer] Mapped {/user-school/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.811 [INFO] [RouterExplorer] Mapped {/user-school/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.811 [INFO] [RoutesResolver] UserStudentController {/user-student}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.811 [INFO] [RouterExplorer] Mapped {/user-student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.811 [INFO] [RouterExplorer] Mapped {/user-student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.812 [INFO] [RouterExplorer] Mapped {/user-student/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.812 [INFO] [RouterExplorer] Mapped {/user-student/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.813 [INFO] [RouterExplorer] Mapped {/user-student/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.813 [INFO] [RouterExplorer] Mapped {/user-student/school/:schoolId/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.813 [INFO] [RouterExplorer] Mapped {/user-student/number, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.814 [INFO] [RouterExplorer] Mapped {/user-student/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.814 [INFO] [RouterExplorer] Mapped {/user-student/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.814 [INFO] [RouterExplorer] Mapped {/user-student/:id/class/:classId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.815 [INFO] [RouterExplorer] Mapped {/user-student/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.815 [INFO] [RouterExplorer] Mapped {/user-student/user/:userId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.815 [INFO] [RoutesResolver] RedisController {/redis}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.815 [INFO] [RouterExplorer] Mapped {/redis/set, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.816 [INFO] [RouterExplorer] Mapped {/redis/get, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.816 [INFO] [RouterExplorer] Mapped {/redis/del, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.816 [INFO] [RouterExplorer] Mapped {/redis/exists, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.816 [INFO] [RouterExplorer] Mapped {/redis/expire, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.816 [INFO] [RoutesResolver] KeyPackageController {/key-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.817 [INFO] [RouterExplorer] Mapped {/key-package, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.817 [INFO] [RouterExplorer] Mapped {/key-package, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.817 [INFO] [RouterExplorer] Mapped {/key-package/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.817 [INFO] [RouterExplorer] Mapped {/key-package/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.818 [INFO] [RouterExplorer] Mapped {/key-package/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.818 [INFO] [RoutesResolver] UserAuthController {/api/user-auth}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.818 [INFO] [RouterExplorer] Mapped {/api/user-auth/bindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.818 [INFO] [RouterExplorer] Mapped {/api/user-auth/setPasswordByPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.819 [INFO] [RouterExplorer] Mapped {/api/user-auth/setPasswordByUserId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.819 [INFO] [RouterExplorer] Mapped {/api/user-auth/password, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.819 [INFO] [RouterExplorer] Mapped {/api/user-auth/select-identity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.819 [INFO] [RouterExplorer] Mapped {/api/user-auth/refreshToken, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.820 [INFO] [RouterExplorer] Mapped {/api/user-auth/updatePassword, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.820 [INFO] [RouterExplorer] Mapped {/api/user-auth/logout, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.820 [INFO] [RouterExplorer] Mapped {/api/user-auth/student, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.820 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsSend, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.821 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsSendUpdatePhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.822 [INFO] [RouterExplorer] Mapped {/api/user-auth/smsLogin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.822 [INFO] [RouterExplorer] Mapped {/api/user-auth/resetPasswordByCode, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.822 [INFO] [RouterExplorer] Mapped {/api/user-auth/student/reset-password, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.822 [INFO] [RouterExplorer] Mapped {/api/user-auth/check-phone-accounts, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.823 [INFO] [RouterExplorer] Mapped {/api/user-auth/findAllBindByPhone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.823 [INFO] [RouterExplorer] Mapped {/api/user-auth/bind-weixin-to-user, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.823 [INFO] [RouterExplorer] Mapped {/api/user-auth/register-and-bind-weixin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.824 [INFO] [RouterExplorer] Mapped {/api/user-auth/bind-weixin, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.824 [INFO] [RouterExplorer] Mapped {/api/user-auth/reset-password-by-phone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.824 [INFO] [RouterExplorer] Mapped {/api/user-auth/switch-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.824 [INFO] [RouterExplorer] Mapped {/api/user-auth/verify-sms-code, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.825 [INFO] [RouterExplorer] Mapped {/api/user-auth/transfer-weixin-openid, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.825 [INFO] [RouterExplorer] Mapped {/api/user-auth/auto-login-by-userid, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.825 [INFO] [RoutesResolver] AliSmsController {/ali-sms}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.826 [INFO] [RouterExplorer] Mapped {/ali-sms/send-code, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.826 [INFO] [RoutesResolver] UserInfoController {/api/user-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.826 [INFO] [RouterExplorer] Mapped {/api/user-info/updatePhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.826 [INFO] [RouterExplorer] Mapped {/api/user-info/condition/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.827 [INFO] [RouterExplorer] Mapped {/api/user-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.827 [INFO] [RouterExplorer] Mapped {/api/user-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.827 [INFO] [RouterExplorer] Mapped {/api/user-info/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.827 [INFO] [RouterExplorer] Mapped {/api/user-info/phone/:phone, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.828 [INFO] [RouterExplorer] Mapped {/api/user-info/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.828 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.828 [INFO] [RouterExplorer] Mapped {/api/user-info/detail/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.829 [INFO] [RouterExplorer] Mapped {/api/user-info/person/info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.829 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.829 [INFO] [RouterExplorer] Mapped {/api/user-info/:id/points/:points, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.830 [INFO] [RouterExplorer] Mapped {/api/user-info/:id/role/:roleId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.830 [INFO] [RouterExplorer] Mapped {/api/user-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.830 [INFO] [RoutesResolver] UserRoleRelationController {/user-role-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.831 [INFO] [RouterExplorer] Mapped {/user-role-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.831 [INFO] [RouterExplorer] Mapped {/user-role-relation/batch-assign-roles, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.832 [INFO] [RouterExplorer] Mapped {/user-role-relation/batch-assign-users, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.832 [INFO] [RouterExplorer] Mapped {/user-role-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.832 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.833 [INFO] [RouterExplorer] Mapped {/user-role-relation/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.833 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.833 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.833 [INFO] [RouterExplorer] Mapped {/user-role-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.834 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.834 [INFO] [RouterExplorer] Mapped {/user-role-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.834 [INFO] [RouterExplorer] Mapped {/user-role-relation/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.834 [INFO] [RoutesResolver] WebWeixinScanController {/weixin-scan}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.835 [INFO] [RouterExplorer] Mapped {/weixin-scan/qrcode, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.835 [INFO] [RouterExplorer] Mapped {/weixin-scan/check-status/:scene_str, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.835 [INFO] [RouterExplorer] Mapped {/weixin-scan/confirm, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.836 [INFO] [RouterExplorer] Mapped {/weixin-scan/callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.836 [INFO] [RoutesResolver] AliOssController {/ali-oss}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.836 [INFO] [RouterExplorer] Mapped {/ali-oss/upload, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.836 [INFO] [RouterExplorer] Mapped {/ali-oss/upload-form, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.837 [INFO] [RouterExplorer] Mapped {/ali-oss/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.837 [INFO] [RouterExplorer] Mapped {/ali-oss/buckets, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.837 [INFO] [RouterExplorer] Mapped {/ali-oss/check-image, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.837 [INFO] [RoutesResolver] UserJoinRoleController {/user-join-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.838 [INFO] [RouterExplorer] Mapped {/user-join-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.838 [INFO] [RouterExplorer] Mapped {/user-join-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.838 [INFO] [RouterExplorer] Mapped {/user-join-role/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.839 [INFO] [RouterExplorer] Mapped {/user-join-role/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.839 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.840 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.840 [INFO] [RouterExplorer] Mapped {/user-join-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.841 [INFO] [RouterExplorer] Mapped {/user-join-role/user/:userId/role/:roleId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.841 [INFO] [RoutesResolver] UserSrchTemplatesController {/api/user/srch/templates}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.842 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.842 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.843 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.843 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/current/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.843 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/batch-current, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.843 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.844 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/list/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.844 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.844 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/:id/default, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.844 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/official/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.845 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/scratch/permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.845 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/scratchs/permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.845 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/tree, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.845 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.846 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.846 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.846 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.846 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.847 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/addTemplate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.847 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/removeTemplate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.847 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/addTemplates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.847 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/teacher_students, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.848 [INFO] [RouterExplorer] Mapped {/api/user/srch/templates/folders/:folderId/templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.848 [INFO] [RoutesResolver] RoleTemplateExtensionPermissionController {/role-template-extension-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.848 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.849 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.849 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.849 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.849 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.850 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.850 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.850 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.850 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId/extension/:extensionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.851 [INFO] [RouterExplorer] Mapped {/role-template-extension-permission/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.851 [INFO] [RoutesResolver] RoleTemplateBlockPermissionController {/role-template-block-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.851 [INFO] [RouterExplorer] Mapped {/role-template-block-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.851 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.852 [INFO] [RouterExplorer] Mapped {/role-template-block-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.852 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.852 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.852 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.853 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.853 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.853 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.853 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/extension/:extensionId/block/:blockId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.854 [INFO] [RouterExplorer] Mapped {/role-template-block-permission/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.854 [INFO] [RoutesResolver] RoleTemplateFolderController {/role-template-folder}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.854 [INFO] [RouterExplorer] Mapped {/role-template-folder, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.854 [INFO] [RouterExplorer] Mapped {/role-template-folder, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.854 [INFO] [RouterExplorer] Mapped {/role-template-folder/root, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.855 [INFO] [RouterExplorer] Mapped {/role-template-folder/tree, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.855 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.855 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/children, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.855 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.856 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/sort/:sort, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.856 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/move, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.856 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.856 [INFO] [RouterExplorer] Mapped {/role-template-folder/:id/with-children, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.857 [INFO] [RoutesResolver] RoleTemplateFolderJoinTemplateController {/role-template-folder-join-template}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.857 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.857 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/batch-templates-to-folder, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.857 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/batch-folders-to-template, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.858 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.858 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.858 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/template/:templateId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.858 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.859 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.859 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.859 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId/template/:templateId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.859 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/folder/:folderId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.860 [INFO] [RouterExplorer] Mapped {/role-template-folder-join-template/template/:templateId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.860 [INFO] [RoutesResolver] TableJoingController {/table-joing}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.860 [INFO] [RouterExplorer] Mapped {/table-joing/student-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.861 [INFO] [RoutesResolver] RouterGuardController {/api/router-guard}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.861 [INFO] [RouterExplorer] Mapped {/api/router-guard/protected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.861 [INFO] [RouterExplorer] Mapped {/api/router-guard/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.861 [INFO] [RouterExplorer] Mapped {/api/router-guard/login-check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.862 [INFO] [RouterExplorer] Mapped {/api/router-guard/refresh-token, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.862 [INFO] [RouterExplorer] Mapped {/api/router-guard/protected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.862 [INFO] [RoutesResolver] UserSrchWorkController {/api/user/srch/work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.862 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.863 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.863 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.863 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.863 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/remove/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.864 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/class/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.864 [INFO] [RouterExplorer] Mapped {/api/user/srch/work/stats/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.864 [INFO] [RoutesResolver] UserWorkInfoController {/user-work-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.865 [INFO] [RouterExplorer] Mapped {/user-work-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.865 [INFO] [RouterExplorer] Mapped {/user-work-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.865 [INFO] [RouterExplorer] Mapped {/user-work-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.865 [INFO] [RouterExplorer] Mapped {/user-work-info/user/:userId/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.866 [INFO] [RouterExplorer] Mapped {/user-work-info/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.866 [INFO] [RouterExplorer] Mapped {/user-work-info/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.866 [INFO] [RouterExplorer] Mapped {/user-work-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.866 [INFO] [RouterExplorer] Mapped {/user-work-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.866 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.867 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.867 [INFO] [RouterExplorer] Mapped {/user-work-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.867 [INFO] [RoutesResolver] WorkModelController {/work-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.867 [INFO] [RouterExplorer] Mapped {/work-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.868 [INFO] [RouterExplorer] Mapped {/work-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.868 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.868 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId/type/:modelType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.868 [INFO] [RouterExplorer] Mapped {/work-model/work/:workId/number/:modelNumber, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.869 [INFO] [RouterExplorer] Mapped {/work-model/:id/toggle-active, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.869 [INFO] [RouterExplorer] Mapped {/work-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.869 [INFO] [RouterExplorer] Mapped {/work-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.869 [INFO] [RouterExplorer] Mapped {/work-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.870 [INFO] [RoutesResolver] UserSrchTaskController {/api/user/srch/task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.870 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:workId/submissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.870 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/stats/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.871 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/batch-assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.871 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.871 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/grade, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.871 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:assignmentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.871 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/return, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.872 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/status/:assignmentId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.872 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/publish-to-class, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.872 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/mark-as-read, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.872 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/task/stats/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.873 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.873 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.873 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.873 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/updata, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.874 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/remove/:taskId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.874 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.874 [INFO] [RouterExplorer] Mapped {/api/user/srch/task/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.874 [INFO] [RoutesResolver] TeacherTaskAssignmentController {/teacher-task-assignment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.875 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.875 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.875 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.875 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.876 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.876 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.876 [INFO] [RouterExplorer] Mapped {/teacher-task-assignment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.877 [INFO] [RoutesResolver] TeacherTaskController {/teacher-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.877 [INFO] [RouterExplorer] Mapped {/teacher-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.877 [INFO] [RouterExplorer] Mapped {/teacher-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.877 [INFO] [RouterExplorer] Mapped {/teacher-task/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.878 [INFO] [RouterExplorer] Mapped {/teacher-task/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.878 [INFO] [RouterExplorer] Mapped {/teacher-task/check-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.878 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.878 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.878 [INFO] [RouterExplorer] Mapped {/teacher-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.879 [INFO] [RoutesResolver] UserImageInfoController {/user-image-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.879 [INFO] [RouterExplorer] Mapped {/user-image-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.879 [INFO] [RouterExplorer] Mapped {/user-image-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.880 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.880 [INFO] [RouterExplorer] Mapped {/user-image-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.880 [INFO] [RouterExplorer] Mapped {/user-image-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.881 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/view, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.881 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/like, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.881 [INFO] [RouterExplorer] Mapped {/user-image-info/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.882 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.882 [INFO] [RouterExplorer] Mapped {/user-image-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.882 [INFO] [RoutesResolver] TaskSelfAssessmentItemController {/api/task-self-assessment-item}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.883 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.883 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.883 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.883 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.884 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.884 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.884 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.884 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/check-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.885 [INFO] [RouterExplorer] Mapped {/api/task-self-assessment-item/update-by-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.885 [INFO] [RoutesResolver] WebUserInfoController {/api/web/user/info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.885 [INFO] [RouterExplorer] Mapped {/api/web/user/info/ping, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.885 [INFO] [RouterExplorer] Mapped {/api/web/user/info/checkHasBindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.886 [INFO] [RouterExplorer] Mapped {/api/web/user/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.886 [INFO] [RouterExplorer] Mapped {/api/web/user/info/update, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.886 [INFO] [RouterExplorer] Mapped {/api/web/user/info/update/password, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.886 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset_password/request, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.887 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset/password/handle, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.887 [INFO] [RouterExplorer] Mapped {/api/web/user/info/reset/password/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.887 [INFO] [RoutesResolver] UserPasswordResetRequestController {/user-password-reset-request}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.887 [INFO] [RouterExplorer] Mapped {/user-password-reset-request, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.888 [INFO] [RouterExplorer] Mapped {/user-password-reset-request, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.888 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.888 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.888 [INFO] [RouterExplorer] Mapped {/user-password-reset-request/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.889 [INFO] [RoutesResolver] UserSrchImageController {/api/user-srch-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.889 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.889 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.889 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.889 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.890 [INFO] [RouterExplorer] Mapped {/api/user-srch-image/task-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.890 [INFO] [RoutesResolver] UserSrchEnhanceController {/api/user-srch-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.890 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.890 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.891 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/byImageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.891 [INFO] [RouterExplorer] Mapped {/api/user-srch-enhance/task-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.891 [INFO] [RoutesResolver] UserImageEnhanceController {/user-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.891 [INFO] [RouterExplorer] Mapped {/user-image-enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.892 [INFO] [RouterExplorer] Mapped {/user-image-enhance, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.894 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.894 [INFO] [RouterExplorer] Mapped {/user-image-enhance/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.895 [INFO] [RouterExplorer] Mapped {/user-image-enhance/image/:imageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.895 [INFO] [RouterExplorer] Mapped {/user-image-enhance/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.895 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.895 [INFO] [RouterExplorer] Mapped {/user-image-enhance/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.896 [INFO] [RoutesResolver] UserSrchAudioController {/api/user-srch-audio}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.896 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.896 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.896 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.897 [INFO] [RouterExplorer] Mapped {/api/user-srch-audio/task-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.897 [INFO] [RoutesResolver] UserVoiceInfoController {/user-voice-info}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.897 [INFO] [RouterExplorer] Mapped {/user-voice-info, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.897 [INFO] [RouterExplorer] Mapped {/user-voice-info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.898 [INFO] [RouterExplorer] Mapped {/user-voice-info/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.898 [INFO] [RouterExplorer] Mapped {/user-voice-info/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.898 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.898 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.899 [INFO] [RouterExplorer] Mapped {/user-voice-info/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.899 [INFO] [RoutesResolver] UserPointPackageController {/user-point-package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.899 [INFO] [RouterExplorer] Mapped {/user-point-package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.900 [INFO] [RouterExplorer] Mapped {/user-point-package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.900 [INFO] [RouterExplorer] Mapped {/user-point-package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.900 [INFO] [RouterExplorer] Mapped {/user-point-package/clean-expired, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.900 [INFO] [RouterExplorer] Mapped {/user-point-package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.901 [INFO] [RoutesResolver] WebPointController {/web/point}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.901 [INFO] [RouterExplorer] Mapped {/web/point/package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.901 [INFO] [RouterExplorer] Mapped {/web/point/package/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.901 [INFO] [RouterExplorer] Mapped {/web/point/package/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.901 [INFO] [RouterExplorer] Mapped {/web/point/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.902 [INFO] [RouterExplorer] Mapped {/web/point/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.902 [INFO] [RouterExplorer] Mapped {/web/point/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.902 [INFO] [RouterExplorer] Mapped {/web/point/assign/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.902 [INFO] [RouterExplorer] Mapped {/web/point/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.903 [INFO] [RouterExplorer] Mapped {/web/point/permission/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.903 [INFO] [RouterExplorer] Mapped {/web/point/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.903 [INFO] [RouterExplorer] Mapped {/web/point/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.903 [INFO] [RouterExplorer] Mapped {/web/point/clean-expired, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.904 [INFO] [RouterExplorer] Mapped {/web/point/clean-expired-permissions, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.904 [INFO] [RoutesResolver] UserSchoolController {/api/user-school}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.904 [INFO] [RouterExplorer] Mapped {/api/user-school, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.904 [INFO] [RouterExplorer] Mapped {/api/user-school/listByUserId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.905 [INFO] [RouterExplorer] Mapped {/api/user-school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.905 [INFO] [RouterExplorer] Mapped {/api/user-school/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.905 [INFO] [RouterExplorer] Mapped {/api/user-school/area, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.905 [INFO] [RouterExplorer] Mapped {/api/user-school/provinces, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.906 [INFO] [RouterExplorer] Mapped {/api/user-school/cities/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.906 [INFO] [RouterExplorer] Mapped {/api/user-school/districts/:province/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.906 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.906 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province/city/:city, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.907 [INFO] [RouterExplorer] Mapped {/api/user-school/province/:province/city/:city/district/:district, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.907 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.907 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.907 [INFO] [RouterExplorer] Mapped {/api/user-school/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.908 [INFO] [RoutesResolver] UserSchoolRelationController {/api/user-school-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.908 [INFO] [RouterExplorer] Mapped {/api/user-school-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.908 [INFO] [RouterExplorer] Mapped {/api/user-school-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.908 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.909 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.909 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.909 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.910 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.910 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.910 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.910 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id/role-type/:roleType, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.911 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.911 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId/school/:schoolId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.911 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.911 [INFO] [RouterExplorer] Mapped {/api/user-school-relation/school/:schoolId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.911 [INFO] [RoutesResolver] WebAnnouncementController {/api/web/announcement}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.912 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/deleteByUser, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.912 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/deleteByAnnouncement, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.912 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/unread, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.912 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/mark, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.912 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.913 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/delete/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.913 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/update/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.913 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.914 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.914 [INFO] [RouterExplorer] Mapped {/api/web/announcement/read/page, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.914 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.914 [INFO] [RouterExplorer] Mapped {/api/web/announcement/list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.915 [INFO] [RouterExplorer] Mapped {/api/web/announcement/recall, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.915 [INFO] [RouterExplorer] Mapped {/api/web/announcement/allIds, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.915 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publishedIds, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.915 [INFO] [RouterExplorer] Mapped {/api/web/announcement/publishedIdsByTarget, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.916 [INFO] [RouterExplorer] Mapped {/api/web/announcement/detail, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.916 [INFO] [RouterExplorer] Mapped {/api/web/announcement/increaseReadCount, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.916 [INFO] [RouterExplorer] Mapped {/api/web/announcement/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.916 [INFO] [RouterExplorer] Mapped {/api/web/announcement/review/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.917 [INFO] [RouterExplorer] Mapped {/api/web/announcement/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.917 [INFO] [RouterExplorer] Mapped {/api/web/announcement/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.917 [INFO] [RouterExplorer] Mapped {/api/web/announcement/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.917 [INFO] [RouterExplorer] Mapped {/api/web/announcement/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.917 [INFO] [RouterExplorer] Mapped {/api/web/announcement/page, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.918 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/listByAnnouncement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.918 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.918 [INFO] [RouterExplorer] Mapped {/api/web/announcement/audit/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.918 [INFO] [RoutesResolver] AnnouncementReadRecordController {/announcement-read-record}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.919 [INFO] [RouterExplorer] Mapped {/announcement-read-record, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.919 [INFO] [RouterExplorer] Mapped {/announcement-read-record, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.919 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.920 [INFO] [RouterExplorer] Mapped {/announcement-read-record/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.920 [INFO] [RouterExplorer] Mapped {/announcement-read-record/announcement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.920 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.921 [INFO] [RouterExplorer] Mapped {/announcement-read-record/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.922 [INFO] [RoutesResolver] AnnouncementController {/announcement}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.922 [INFO] [RouterExplorer] Mapped {/announcement, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.922 [INFO] [RouterExplorer] Mapped {/announcement, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.923 [INFO] [RouterExplorer] Mapped {/announcement/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.923 [INFO] [RouterExplorer] Mapped {/announcement/publisher/:publisherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.923 [INFO] [RouterExplorer] Mapped {/announcement/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.923 [INFO] [RouterExplorer] Mapped {/announcement/top/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.923 [INFO] [RouterExplorer] Mapped {/announcement/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.924 [INFO] [RouterExplorer] Mapped {/announcement/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.924 [INFO] [RouterExplorer] Mapped {/announcement/:id/read, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.924 [INFO] [RouterExplorer] Mapped {/announcement/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.924 [INFO] [RoutesResolver] AnnouncementAuditController {/announcement-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.925 [INFO] [RouterExplorer] Mapped {/announcement-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.925 [INFO] [RouterExplorer] Mapped {/announcement-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.925 [INFO] [RouterExplorer] Mapped {/announcement-audit/announcement/:announcementId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.925 [INFO] [RouterExplorer] Mapped {/announcement-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.925 [INFO] [RouterExplorer] Mapped {/announcement-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.926 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.926 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.926 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.926 [INFO] [RouterExplorer] Mapped {/announcement-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.927 [INFO] [RoutesResolver] WebCarouselController {/api/web-carousel}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.927 [INFO] [RouterExplorer] Mapped {/api/web-carousel/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.927 [INFO] [RouterExplorer] Mapped {/api/web-carousel, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.928 [INFO] [RouterExplorer] Mapped {/api/web-carousel/review, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.928 [INFO] [RouterExplorer] Mapped {/api/web-carousel/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.928 [INFO] [RouterExplorer] Mapped {/api/web-carousel/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.928 [INFO] [RouterExplorer] Mapped {/api/web-carousel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.929 [INFO] [RouterExplorer] Mapped {/api/web-carousel/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.929 [INFO] [RouterExplorer] Mapped {/api/web-carousel/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.929 [INFO] [RoutesResolver] WebCarouselAuditController {/api/web-carousel/audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.929 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/listByCarousel/:carouselId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.930 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/listWithFilter, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.930 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.930 [INFO] [RouterExplorer] Mapped {/api/web-carousel/audit/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.930 [INFO] [RoutesResolver] SpaceCarouselMapController {/space-carousel-map}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.930 [INFO] [RouterExplorer] Mapped {/space-carousel-map, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.931 [INFO] [RouterExplorer] Mapped {/space-carousel-map, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.931 [INFO] [RouterExplorer] Mapped {/space-carousel-map/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.931 [INFO] [RouterExplorer] Mapped {/space-carousel-map/type/:type, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.931 [INFO] [RouterExplorer] Mapped {/space-carousel-map/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.932 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.932 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id/sort/:sort, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.932 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.932 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.932 [INFO] [RouterExplorer] Mapped {/space-carousel-map/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.933 [INFO] [RoutesResolver] CarouselAuditController {/carousel-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.933 [INFO] [RouterExplorer] Mapped {/carousel-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.933 [INFO] [RouterExplorer] Mapped {/carousel-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.933 [INFO] [RouterExplorer] Mapped {/carousel-audit/carousel/:carouselId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.934 [INFO] [RouterExplorer] Mapped {/carousel-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.934 [INFO] [RouterExplorer] Mapped {/carousel-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.934 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.934 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.935 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.935 [INFO] [RouterExplorer] Mapped {/carousel-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.935 [INFO] [RoutesResolver] TeacherTaskController {/api/teacher-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.935 [INFO] [RouterExplorer] Mapped {/api/teacher-task/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.935 [INFO] [RouterExplorer] Mapped {/api/teacher-task/delete-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.936 [INFO] [RouterExplorer] Mapped {/api/teacher-task/update-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.936 [INFO] [RouterExplorer] Mapped {/api/teacher-task/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.936 [INFO] [RouterExplorer] Mapped {/api/teacher-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.937 [INFO] [RouterExplorer] Mapped {/api/teacher-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.937 [INFO] [RouterExplorer] Mapped {/api/teacher-task/teacher/:teacherId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.937 [INFO] [RouterExplorer] Mapped {/api/teacher-task/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.937 [INFO] [RouterExplorer] Mapped {/api/teacher-task/check-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.937 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.938 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.938 [INFO] [RouterExplorer] Mapped {/api/teacher-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.938 [INFO] [RouterExplorer] Mapped {/api/teacher-task/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.938 [INFO] [RoutesResolver] WebRoleController {/api/web-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.939 [INFO] [RouterExplorer] Mapped {/api/web-role/user/join/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.939 [INFO] [RouterExplorer] Mapped {/api/web-role/template/teacher/default, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.939 [INFO] [RouterExplorer] Mapped {/api/web-role/template/official/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.939 [INFO] [RouterExplorer] Mapped {/api/web-role/template/official/list/page, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.940 [INFO] [RoutesResolver] UserPasswordResetController {/api/user-password-reset}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.940 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.940 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.940 [INFO] [RouterExplorer] Mapped {/api/user-password-reset/handle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.940 [INFO] [RoutesResolver] WebPermissionController {/api/web-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.941 [INFO] [RouterExplorer] Mapped {/api/web-permission/extension/available, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.941 [INFO] [RouterExplorer] Mapped {/api/web-permission/block/byExtension, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.941 [INFO] [RouterExplorer] Mapped {/api/web-permission/getUserPermissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.942 [INFO] [RouterExplorer] Mapped {/api/web-permission/updateExtensionPermission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.942 [INFO] [RouterExplorer] Mapped {/api/web-permission/updateBlockPermission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.942 [INFO] [RouterExplorer] Mapped {/api/web-permission/role-templates, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.942 [INFO] [RouterExplorer] Mapped {/api/web-permission/user/:userId/role/:roleId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.943 [INFO] [RouterExplorer] Mapped {/api/web-permission/template-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.943 [INFO] [RoutesResolver] BlockController {/block}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.943 [INFO] [RouterExplorer] Mapped {/block, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.943 [INFO] [RouterExplorer] Mapped {/block, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.943 [INFO] [RouterExplorer] Mapped {/block/block-id/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.944 [INFO] [RouterExplorer] Mapped {/block/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.944 [INFO] [RouterExplorer] Mapped {/block/type/:blockType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.944 [INFO] [RouterExplorer] Mapped {/block/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.944 [INFO] [RouterExplorer] Mapped {/block/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.944 [INFO] [RouterExplorer] Mapped {/block/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.945 [INFO] [RoutesResolver] ExtensionsController {/extensions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.945 [INFO] [RouterExplorer] Mapped {/extensions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.945 [INFO] [RouterExplorer] Mapped {/extensions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.946 [INFO] [RouterExplorer] Mapped {/extensions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.946 [INFO] [RouterExplorer] Mapped {/extensions/extensionId/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.946 [INFO] [RouterExplorer] Mapped {/extensions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.946 [INFO] [RouterExplorer] Mapped {/extensions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.947 [INFO] [RoutesResolver] ExtensionPermissionsController {/extension-permissions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.947 [INFO] [RouterExplorer] Mapped {/extension-permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.947 [INFO] [RouterExplorer] Mapped {/extension-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.948 [INFO] [RouterExplorer] Mapped {/extension-permissions/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.948 [INFO] [RouterExplorer] Mapped {/extension-permissions/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.948 [INFO] [RouterExplorer] Mapped {/extension-permissions/active/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.948 [INFO] [RouterExplorer] Mapped {/extension-permissions/check/:userId/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.949 [INFO] [RouterExplorer] Mapped {/extension-permissions/batch/:extensionId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.949 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/enable, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.949 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/disable, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.949 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id/expire, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.950 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.950 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.950 [INFO] [RouterExplorer] Mapped {/extension-permissions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.950 [INFO] [RoutesResolver] BlockPermissionsController {/block-permissions}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.950 [INFO] [RouterExplorer] Mapped {/block-permissions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.951 [INFO] [RouterExplorer] Mapped {/block-permissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.951 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.951 [INFO] [RouterExplorer] Mapped {/block-permissions/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.951 [INFO] [RouterExplorer] Mapped {/block-permissions/extension/:extensionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.952 [INFO] [RouterExplorer] Mapped {/block-permissions/block/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.952 [INFO] [RouterExplorer] Mapped {/block-permissions/check/:userId/:blockId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.952 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.952 [INFO] [RouterExplorer] Mapped {/block-permissions/:id/toggle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.952 [INFO] [RouterExplorer] Mapped {/block-permissions/:id/expire, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.953 [INFO] [RouterExplorer] Mapped {/block-permissions/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.953 [INFO] [RoutesResolver] WebReportController {/api/web/report}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.953 [INFO] [RouterExplorer] Mapped {/api/web/report/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.953 [INFO] [RouterExplorer] Mapped {/api/web/report/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.954 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.954 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/:id/handle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.954 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/batchHandle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.954 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.955 [INFO] [RouterExplorer] Mapped {/api/web/report/admin/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.955 [INFO] [RoutesResolver] UserReportController {/user-report}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.955 [INFO] [RouterExplorer] Mapped {/user-report, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.955 [INFO] [RouterExplorer] Mapped {/user-report, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.956 [INFO] [RouterExplorer] Mapped {/user-report/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.956 [INFO] [RouterExplorer] Mapped {/user-report/reporter/:reporterId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.956 [INFO] [RouterExplorer] Mapped {/user-report/target/:targetId/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.956 [INFO] [RouterExplorer] Mapped {/user-report/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.957 [INFO] [RouterExplorer] Mapped {/user-report/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.957 [INFO] [RouterExplorer] Mapped {/user-report/:id/handle, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.957 [INFO] [RouterExplorer] Mapped {/user-report/:id/reject, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.957 [INFO] [RouterExplorer] Mapped {/user-report/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.958 [INFO] [RoutesResolver] WebTemplateFolderController {/web-template-folder}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.958 [INFO] [RoutesResolver] WebDocController {/api/web/doc}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.958 [INFO] [RouterExplorer] Mapped {/api/web/doc/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.958 [INFO] [RouterExplorer] Mapped {/api/web/doc/getByDocId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.959 [INFO] [RouterExplorer] Mapped {/api/web/doc/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.959 [INFO] [RouterExplorer] Mapped {/api/web/doc/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.959 [INFO] [RouterExplorer] Mapped {/api/web/doc/getUserDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.960 [INFO] [RouterExplorer] Mapped {/api/web/doc/getDocById, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.960 [INFO] [RouterExplorer] Mapped {/api/web/doc/getAllDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.960 [INFO] [RouterExplorer] Mapped {/api/web/doc/findByTitle, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.961 [INFO] [RouterExplorer] Mapped {/api/web/doc/findByTag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.961 [INFO] [RouterExplorer] Mapped {/api/web/doc/findPublicDocs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.961 [INFO] [RoutesResolver] DocController {/doc}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.962 [INFO] [RouterExplorer] Mapped {/doc, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.962 [INFO] [RouterExplorer] Mapped {/doc, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.962 [INFO] [RouterExplorer] Mapped {/doc/doc-id/:docId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.962 [INFO] [RouterExplorer] Mapped {/doc/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.963 [INFO] [RouterExplorer] Mapped {/doc/search/title, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.963 [INFO] [RouterExplorer] Mapped {/doc/tag/:tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.963 [INFO] [RouterExplorer] Mapped {/doc/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.963 [INFO] [RouterExplorer] Mapped {/doc/visible/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.964 [INFO] [RouterExplorer] Mapped {/doc/:id/soft-remove, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.964 [INFO] [RouterExplorer] Mapped {/doc/:id/restore, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.964 [INFO] [RouterExplorer] Mapped {/doc/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.964 [INFO] [RouterExplorer] Mapped {/doc/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.965 [INFO] [RouterExplorer] Mapped {/doc/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.965 [INFO] [RoutesResolver] WebKeyPackageController {/api/web/key_package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.965 [INFO] [RouterExplorer] Mapped {/api/web/key_package/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.965 [INFO] [RouterExplorer] Mapped {/api/web/key_package/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.966 [INFO] [RouterExplorer] Mapped {/api/web/key_package/validate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.966 [INFO] [RouterExplorer] Mapped {/api/web/key_package/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.966 [INFO] [RouterExplorer] Mapped {/api/web/key_package/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.966 [INFO] [RouterExplorer] Mapped {/api/web/key_package/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.967 [INFO] [RouterExplorer] Mapped {/api/web/key_package/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.967 [INFO] [RouterExplorer] Mapped {/api/web/key_package/info, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.967 [INFO] [RouterExplorer] Mapped {/api/web/key_package/batch-create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.967 [INFO] [RoutesResolver] KeyPackageRecordController {/key-package-record}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.968 [INFO] [RouterExplorer] Mapped {/key-package-record, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.968 [INFO] [RouterExplorer] Mapped {/key-package-record, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.968 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.968 [INFO] [RouterExplorer] Mapped {/key-package-record/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.968 [INFO] [RouterExplorer] Mapped {/key-package-record/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.969 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.969 [INFO] [RouterExplorer] Mapped {/key-package-record/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.969 [INFO] [RoutesResolver] WebPackageController {/api/web/package}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.969 [INFO] [RouterExplorer] Mapped {/api/web/package/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.970 [INFO] [RouterExplorer] Mapped {/api/web/package/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.970 [INFO] [RouterExplorer] Mapped {/api/web/package/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.970 [INFO] [RouterExplorer] Mapped {/api/web/package/update, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.970 [INFO] [RouterExplorer] Mapped {/api/web/package/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.971 [INFO] [RouterExplorer] Mapped {/api/web/package/available, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.971 [INFO] [RouterExplorer] Mapped {/api/web/package/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.971 [INFO] [RouterExplorer] Mapped {/api/web/package/user/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.971 [INFO] [RouterExplorer] Mapped {/api/web/package/user/current, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.972 [INFO] [RouterExplorer] Mapped {/api/web/package/user/message-center, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.972 [INFO] [RouterExplorer] Mapped {/api/web/package/user/details/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.972 [INFO] [RouterExplorer] Mapped {/api/web/package/user/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.972 [INFO] [RoutesResolver] WebPointPermissionController {/api/web-point-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.973 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.973 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/batchAssign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.973 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/student, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.973 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/teacher, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.974 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.974 [INFO] [RouterExplorer] Mapped {/api/web-point-permission/use, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.974 [INFO] [RoutesResolver] OssController {/api/oss}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.974 [INFO] [RouterExplorer] Mapped {/api/oss/upload, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.975 [INFO] [RouterExplorer] Mapped {/api/oss/upload-form, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.975 [INFO] [RouterExplorer] Mapped {/api/oss/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.975 [INFO] [RoutesResolver] UserSrchImageSegmentController {/api/user-srch-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.975 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.975 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/add, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.976 [INFO] [RouterExplorer] Mapped {/api/user-srch-image-segment/status/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.976 [INFO] [RoutesResolver] UserImageSegmentController {/user-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.976 [INFO] [RouterExplorer] Mapped {/user-image-segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.976 [INFO] [RouterExplorer] Mapped {/user-image-segment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.977 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.977 [INFO] [RouterExplorer] Mapped {/user-image-segment/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.977 [INFO] [RouterExplorer] Mapped {/user-image-segment/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.978 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.978 [INFO] [RouterExplorer] Mapped {/user-image-segment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.978 [INFO] [RoutesResolver] WeixinMessageController {/weixin}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.978 [INFO] [RouterExplorer] Mapped {/weixin/message, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.979 [INFO] [RouterExplorer] Mapped {/weixin/message, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.979 [INFO] [RoutesResolver] WeixinController {/api/weixin}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.979 [INFO] [RouterExplorer] Mapped {/api/weixin/bindPhone, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.979 [INFO] [RouterExplorer] Mapped {/api/weixin/checkBindStatus, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.980 [INFO] [RouterExplorer] Mapped {/api/weixin/bindPage, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.980 [INFO] [RoutesResolver] TeacherAuditController {/api/teacher-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.980 [INFO] [RouterExplorer] Mapped {/api/teacher-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.980 [INFO] [RouterExplorer] Mapped {/api/teacher-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.980 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/getTeacherAuthByCondition, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.981 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/teacher, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.981 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.981 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.981 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.982 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/info/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.982 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/name/:name, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.982 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.982 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/search/name-like/:pattern, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.983 [INFO] [RouterExplorer] Mapped {/api/teacher-audit/review, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.983 [INFO] [RoutesResolver] AttachmentController {/api/attachment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.983 [INFO] [RouterExplorer] Mapped {/api/attachment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.983 [INFO] [RouterExplorer] Mapped {/api/attachment/batch, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.984 [INFO] [RouterExplorer] Mapped {/api/attachment, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.984 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.984 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.984 [INFO] [RouterExplorer] Mapped {/api/attachment/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.985 [INFO] [RoutesResolver] SelfAssessmentItemController {/api/self-assessment-item}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.987 [INFO] [RouterExplorer] Mapped {/api/self-assessment-item/task/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.987 [INFO] [RouterExplorer] Mapped {/api/self-assessment-item/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.987 [INFO] [RoutesResolver] ZwwController {/zww}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.988 [INFO] [RouterExplorer] Mapped {/zww, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.988 [INFO] [RouterExplorer] Mapped {/zww, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.988 [INFO] [RouterExplorer] Mapped {/zww/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.988 [INFO] [RouterExplorer] Mapped {/zww/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.989 [INFO] [RouterExplorer] Mapped {/zww/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.989 [INFO] [RoutesResolver] PackageOrderBusinessController {/api/v1/package-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.989 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/purchase, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.989 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/payment-callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.990 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/order-status/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.990 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/my-orders, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.990 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/cancel/:orderNo, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.990 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/packages, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.991 [INFO] [RouterExplorer] Mapped {/api/v1/package-order/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.991 [INFO] [RoutesResolver] PackageOrderController {/package-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.991 [INFO] [RouterExplorer] Mapped {/package-order, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.992 [INFO] [RouterExplorer] Mapped {/package-order, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.992 [INFO] [RouterExplorer] Mapped {/package-order/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.992 [INFO] [RouterExplorer] Mapped {/package-order/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.992 [INFO] [RouterExplorer] Mapped {/package-order/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.992 [INFO] [RouterExplorer] Mapped {/package-order/order-no/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.993 [INFO] [RouterExplorer] Mapped {/package-order/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.993 [INFO] [RouterExplorer] Mapped {/package-order/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.993 [INFO] [RouterExplorer] Mapped {/package-order/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.993 [INFO] [RouterExplorer] Mapped {/package-order/payment/:orderNo, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.993 [INFO] [RouterExplorer] Mapped {/package-order/cancel/:orderNo, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.994 [INFO] [RoutesResolver] PackagePricingController {/package-pricing}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.994 [INFO] [RouterExplorer] Mapped {/package-pricing, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.994 [INFO] [RouterExplorer] Mapped {/package-pricing, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.995 [INFO] [RouterExplorer] Mapped {/package-pricing/current-pricings, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.995 [INFO] [RouterExplorer] Mapped {/package-pricing/package/:packageId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.995 [INFO] [RouterExplorer] Mapped {/package-pricing/package/:packageId/all, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.995 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.996 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.996 [INFO] [RouterExplorer] Mapped {/package-pricing/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.996 [INFO] [RoutesResolver] PaymentController {/v1/payment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.996 [INFO] [RouterExplorer] Mapped {/v1/payment/create, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.997 [INFO] [RouterExplorer] Mapped {/v1/payment/query/:outTradeNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.997 [INFO] [RouterExplorer] Mapped {/v1/payment/close, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.997 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/:channel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.997 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/notify/:channel, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.997 [INFO] [RouterExplorer] Mapped {/v1/payment/return/:channel, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.998 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/wechatpay, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.998 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/alipay/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.998 [INFO] [RouterExplorer] Mapped {/v1/payment/notify/wechatpay/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.998 [INFO] [RouterExplorer] Mapped {/v1/payment/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.999 [INFO] [RouterExplorer] Mapped {/v1/payment/test/wechat/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.999 [INFO] [RoutesResolver] RefundController {/v1/payment/refund}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.999 [INFO] [RouterExplorer] Mapped {/v1/payment/refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:24.999 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/query, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.000 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/:refundNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.000 [INFO] [RouterExplorer] Mapped {/v1/payment/refund/:refundNo/check, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.000 [INFO] [RoutesResolver] PaymentRecordController {/api/v1/payment-records}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.001 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.001 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.001 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.001 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.002 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.002 [INFO] [RouterExplorer] Mapped {/api/v1/payment-records/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.002 [INFO] [RoutesResolver] NotificationRecordController {/api/v1/notification-records}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.002 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.003 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.003 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.003 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.003 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.004 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/retry/:id, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.004 [INFO] [RouterExplorer] Mapped {/api/v1/notification-records/batch/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.004 [INFO] [RoutesResolver] PaymentLogController {/api/v1/payment-logs}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.004 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.005 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.005 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.005 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/order/:orderNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.005 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/refund/:refundNo, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.006 [INFO] [RouterExplorer] Mapped {/api/v1/payment-logs/cleanup, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.006 [INFO] [RoutesResolver] PaymentOrderController {/payment-order}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.006 [INFO] [RouterExplorer] Mapped {/payment-order, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.007 [INFO] [RouterExplorer] Mapped {/payment-order, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.007 [INFO] [RouterExplorer] Mapped {/payment-order/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.007 [INFO] [RouterExplorer] Mapped {/payment-order/business-order-id/:businessOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.007 [INFO] [RouterExplorer] Mapped {/payment-order/channel-order-id/:channelOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.008 [INFO] [RouterExplorer] Mapped {/payment-order/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.008 [INFO] [RouterExplorer] Mapped {/payment-order/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.008 [INFO] [RouterExplorer] Mapped {/payment-order/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.008 [INFO] [RouterExplorer] Mapped {/payment-order/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.009 [INFO] [RouterExplorer] Mapped {/payment-order/:id/notify, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.009 [INFO] [RouterExplorer] Mapped {/payment-order/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.009 [INFO] [RoutesResolver] PaymentRefundController {/payment-refund}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.009 [INFO] [RouterExplorer] Mapped {/payment-refund, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.010 [INFO] [RouterExplorer] Mapped {/payment-refund, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.010 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.010 [INFO] [RouterExplorer] Mapped {/payment-refund/business-refund-id/:businessRefundId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.010 [INFO] [RouterExplorer] Mapped {/payment-refund/payment-order/:paymentOrderId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.011 [INFO] [RouterExplorer] Mapped {/payment-refund/channel-refund-id/:channelRefundId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.011 [INFO] [RouterExplorer] Mapped {/payment-refund/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.011 [INFO] [RouterExplorer] Mapped {/payment-refund/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.011 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.012 [INFO] [RouterExplorer] Mapped {/payment-refund/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.012 [INFO] [RouterExplorer] Mapped {/payment-refund/:id/notify, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.012 [INFO] [RouterExplorer] Mapped {/payment-refund/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.012 [INFO] [RoutesResolver] UserLoginLogController {/api/user-login-log}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.012 [INFO] [RouterExplorer] Mapped {/api/user-login-log, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.013 [INFO] [RouterExplorer] Mapped {/api/user-login-log, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.013 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/history, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.013 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/last, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.014 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.014 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/check-abnormal, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.014 [INFO] [RouterExplorer] Mapped {/api/user-login-log/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.014 [INFO] [RouterExplorer] Mapped {/api/user-login-log/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.014 [INFO] [RouterExplorer] Mapped {/api/user-login-log/user/:userId/logout, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.015 [INFO] [RouterExplorer] Mapped {/api/user-login-log/debug/user/:userId/recent, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.015 [INFO] [RoutesResolver] UserTagController {/api/v1/tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.015 [INFO] [RouterExplorer] Mapped {/api/v1/tag/createTag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.016 [INFO] [RouterExplorer] Mapped {/api/v1/tag/updateTag/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.016 [INFO] [RouterExplorer] Mapped {/api/v1/tag/deleteTag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.016 [INFO] [RouterExplorer] Mapped {/api/v1/tag/infoTag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.016 [INFO] [RouterExplorer] Mapped {/api/v1/tag/listTag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.016 [INFO] [RoutesResolver] TagController {/tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.017 [INFO] [RouterExplorer] Mapped {/tag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.017 [INFO] [RouterExplorer] Mapped {/tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.017 [INFO] [RouterExplorer] Mapped {/tag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.017 [INFO] [RouterExplorer] Mapped {/tag/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.017 [INFO] [RouterExplorer] Mapped {/tag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.018 [INFO] [RoutesResolver] WebActivityController {/api/v1/activity}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.018 [INFO] [RouterExplorer] Mapped {/api/v1/activity/createActivity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.018 [INFO] [RouterExplorer] Mapped {/api/v1/activity/updateActivity/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.018 [INFO] [RouterExplorer] Mapped {/api/v1/activity/deleteActivity/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.019 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivity/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.019 [INFO] [RouterExplorer] Mapped {/api/v1/activity/listActivity, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.019 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivityWithWorks/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.019 [INFO] [RouterExplorer] Mapped {/api/v1/activity/infoActivityContent/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.020 [INFO] [RouterExplorer] Mapped {/api/v1/activity/addWorks/:activityId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.020 [INFO] [RouterExplorer] Mapped {/api/v1/activity/setAwardedWorks/:activityId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.020 [INFO] [RouterExplorer] Mapped {/api/v1/activity/uploadSignature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.020 [INFO] [RouterExplorer] Mapped {/api/v1/activity/submitRegistration, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.021 [INFO] [RouterExplorer] Mapped {/api/v1/activity/checkRegistration/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.021 [INFO] [RouterExplorer] Mapped {/api/v1/activity/cancelRegistration/:activityId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.021 [INFO] [RouterExplorer] Mapped {/api/v1/activity/myRegistrations, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.021 [INFO] [RouterExplorer] Mapped {/api/v1/activity/registrationStatistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.021 [INFO] [RoutesResolver] ActivityController {/activity}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.022 [INFO] [RouterExplorer] Mapped {/activity, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.022 [INFO] [RouterExplorer] Mapped {/activity, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.022 [INFO] [RouterExplorer] Mapped {/activity/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.022 [INFO] [RouterExplorer] Mapped {/activity/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.022 [INFO] [RouterExplorer] Mapped {/activity/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.023 [INFO] [RouterExplorer] Mapped {/activity/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.023 [INFO] [RouterExplorer] Mapped {/activity/type/:activityType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.023 [INFO] [RouterExplorer] Mapped {/activity/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.023 [INFO] [RouterExplorer] Mapped {/activity/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.024 [INFO] [RouterExplorer] Mapped {/activity/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.024 [INFO] [RouterExplorer] Mapped {/activity/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.024 [INFO] [RouterExplorer] Mapped {/activity/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.024 [INFO] [RoutesResolver] ActivitySubmitController {/activity-submit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.025 [INFO] [RouterExplorer] Mapped {/activity-submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.025 [INFO] [RouterExplorer] Mapped {/activity-submit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.025 [INFO] [RouterExplorer] Mapped {/activity-submit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.025 [INFO] [RouterExplorer] Mapped {/activity-submit/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.025 [INFO] [RouterExplorer] Mapped {/activity-submit/check/:activityId/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.026 [INFO] [RouterExplorer] Mapped {/activity-submit/statistics/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.026 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.026 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.026 [INFO] [RouterExplorer] Mapped {/activity-submit/:id/cancel, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.027 [INFO] [RouterExplorer] Mapped {/activity-submit/cancel/:activityId/:userId, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.027 [INFO] [RouterExplorer] Mapped {/activity-submit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.027 [INFO] [RoutesResolver] ActivityEventsTaskController {/activity-events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.027 [INFO] [RouterExplorer] Mapped {/activity-events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.027 [INFO] [RouterExplorer] Mapped {/activity-events-task, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.028 [INFO] [RouterExplorer] Mapped {/activity-events-task/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.028 [INFO] [RouterExplorer] Mapped {/activity-events-task/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.028 [INFO] [RouterExplorer] Mapped {/activity-events-task/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.028 [INFO] [RouterExplorer] Mapped {/activity-events-task/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.029 [INFO] [RouterExplorer] Mapped {/activity-events-task/school, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.029 [INFO] [RouterExplorer] Mapped {/activity-events-task/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.029 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.029 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.029 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.030 [INFO] [RouterExplorer] Mapped {/activity-events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.030 [INFO] [RoutesResolver] WebActivityTagController {/api/v1/activity_tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.030 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/add-tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.031 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/edit-tags/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.031 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/remove-tags/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.031 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-activity/:activityId/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.031 [INFO] [RouterExplorer] Mapped {/api/v1/activity_tag/get-tag/:tagId/activities, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.032 [INFO] [RoutesResolver] ActivityTagController {/activity-tag}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.032 [INFO] [RouterExplorer] Mapped {/activity-tag, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.032 [INFO] [RouterExplorer] Mapped {/activity-tag, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.032 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.033 [INFO] [RouterExplorer] Mapped {/activity-tag/tag/:tagId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.033 [INFO] [RouterExplorer] Mapped {/activity-tag/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.033 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.033 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.033 [INFO] [RouterExplorer] Mapped {/activity-tag/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.034 [INFO] [RouterExplorer] Mapped {/activity-tag/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.034 [INFO] [RoutesResolver] WebActivityWorkController {/api/v1/activity_work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.034 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-works, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.034 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/add-image, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.034 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-works/:activityId, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.035 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/check-submitted/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.035 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/list/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.035 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/remove/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.035 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/batch-remove, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.036 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/set-awarded/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.036 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-category/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.036 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/update-status/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.036 [INFO] [RouterExplorer] Mapped {/api/v1/activity_work/cancel/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.037 [INFO] [RoutesResolver] ActivityWorkController {/activity-work}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.037 [INFO] [RouterExplorer] Mapped {/activity-work, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.037 [INFO] [RouterExplorer] Mapped {/activity-work, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.037 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.038 [INFO] [RouterExplorer] Mapped {/activity-work/work/:workId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.038 [INFO] [RouterExplorer] Mapped {/activity-work/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.038 [INFO] [RouterExplorer] Mapped {/activity-work/selected/:isSelected, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.038 [INFO] [RouterExplorer] Mapped {/activity-work/winners, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.038 [INFO] [RouterExplorer] Mapped {/activity-work/:id/selected/:isSelected, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.039 [INFO] [RouterExplorer] Mapped {/activity-work/:id/winner/:isWinner, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.039 [INFO] [RouterExplorer] Mapped {/activity-work/activity/:activityId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.039 [INFO] [RouterExplorer] Mapped {/activity-work/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.039 [INFO] [RouterExplorer] Mapped {/activity-work/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.040 [INFO] [RouterExplorer] Mapped {/activity-work/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.040 [INFO] [RouterExplorer] Mapped {/activity-work/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.040 [INFO] [RoutesResolver] WebEventsTaskController {/api/v1/web/events-task}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.041 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.041 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.041 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/activity/:activityId/tasks, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.042 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/statistics, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.042 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/upcoming, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.042 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/ongoing, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.042 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/status/:status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.042 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/my-tasks/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.043 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.043 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.043 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.043 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/admin-review, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.044 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.044 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id/submit-status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.044 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/batch/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.044 [INFO] [RouterExplorer] Mapped {/api/v1/web/events-task/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.045 [INFO] [RoutesResolver] ManagementController {/api/v1/course-management}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.045 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/my-series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.045 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/courses, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.045 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.046 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.046 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.046 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.046 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/publish, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.047 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.047 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/settings, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.047 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:courseId/task-templates, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.047 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.048 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.048 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/courses/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.048 [INFO] [RouterExplorer] Mapped {/api/v1/course-management/series/:seriesId/course-orders, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.048 [INFO] [RoutesResolver] TeachingController {/api/v1/course-teaching}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.048 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/one-click-start, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.049 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/course-settings/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.049 [INFO] [RouterExplorer] Mapped {/api/v1/course-teaching/records, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.049 [INFO] [RoutesResolver] MarketplaceController {/api/v1/course-marketplace}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.050 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.050 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.050 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/series/:seriesId/courses/:courseId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.050 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.050 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.051 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.051 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.051 [INFO] [RouterExplorer] Mapped {/api/v1/course-marketplace/tags/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.051 [INFO] [RoutesResolver] AiImageGenerateController {/api/ai-image-generate}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.052 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.052 [INFO] [RouterExplorer] Mapped {/api/ai-image-generate/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.052 [INFO] [RoutesResolver] QueueController {/api/queue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.052 [INFO] [RouterExplorer] Mapped {/api/queue/addJob, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.053 [INFO] [RouterExplorer] Mapped {/api/queue/clearAllQueues, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.053 [INFO] [RouterExplorer] Mapped {/api/queue/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.053 [INFO] [RouterExplorer] Mapped {/api/queue/cleanup/:queueName, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.053 [INFO] [RouterExplorer] Mapped {/api/queue/ensure-expire, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.054 [INFO] [RoutesResolver] MinimaxImageController {/api/minimax-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.054 [INFO] [RouterExplorer] Mapped {/api/minimax-image/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.054 [INFO] [RoutesResolver] AiTextDialogueController {/api/ai-text-dialogue}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.054 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.054 [INFO] [RouterExplorer] Mapped {/api/ai-text-dialogue/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.055 [INFO] [RoutesResolver] ZhipuLlmController {/zhipu-llm}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.055 [INFO] [RouterExplorer] Mapped {/zhipu-llm/chat, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.055 [INFO] [RoutesResolver] AliQwenTurboController {/ali-qwen-turbo}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.056 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/stream, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.056 [INFO] [RouterExplorer] Mapped {/ali-qwen-turbo/sse-callback, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.056 [INFO] [RoutesResolver] AiVisualRecognitionController {/api/ai-visual-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.056 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.056 [INFO] [RouterExplorer] Mapped {/api/ai-visual-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.057 [INFO] [RoutesResolver] AliQwenVisionController {/ali-qwen-vision}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.057 [INFO] [RouterExplorer] Mapped {/ali-qwen-vision/analyze, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.057 [INFO] [RoutesResolver] AiExpressionRecognitionController {/api/ai-expression-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.058 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.058 [INFO] [RouterExplorer] Mapped {/api/ai-expression-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.058 [INFO] [RoutesResolver] AliyunExpressionController {/api/aliyun-expression}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.058 [INFO] [RouterExplorer] Mapped {/api/aliyun-expression/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.058 [INFO] [RoutesResolver] AiFaceCompareController {/ai-face-compare}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.059 [INFO] [RouterExplorer] Mapped {/ai-face-compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.059 [INFO] [RouterExplorer] Mapped {/ai-face-compare/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.059 [INFO] [RoutesResolver] AliyunFaceCompareController {/aliyun-face-one-contrast-one}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.059 [INFO] [RouterExplorer] Mapped {/aliyun-face-one-contrast-one/compare, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.060 [INFO] [RoutesResolver] AiFaceRecognitionController {/api/ai-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.060 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.060 [INFO] [RouterExplorer] Mapped {/api/ai-face-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.060 [INFO] [RoutesResolver] AliyunFaceRecognitionController {/api/aliyun-face-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.061 [INFO] [RouterExplorer] Mapped {/api/aliyun-face-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.061 [INFO] [RoutesResolver] AiImageEnhanceController {/api/ai-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.061 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.061 [INFO] [RouterExplorer] Mapped {/api/ai-image-enhance/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.062 [INFO] [RoutesResolver] BaiduImageEnhanceController {/api/baidu-image-enhance}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.062 [INFO] [RouterExplorer] Mapped {/api/baidu-image-enhance/enhance, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.062 [INFO] [RoutesResolver] AiImageScoreController {/api/ai-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.062 [INFO] [RouterExplorer] Mapped {/api/ai-image-score, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.063 [INFO] [RouterExplorer] Mapped {/api/ai-image-score/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.063 [INFO] [RoutesResolver] AliyunImageScoreController {/api/aliyun-image-score}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.063 [INFO] [RouterExplorer] Mapped {/api/aliyun-image-score/assess, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.063 [INFO] [RoutesResolver] AiImageSegmentController {/api/ai-image-segment}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.064 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.064 [INFO] [RouterExplorer] Mapped {/api/ai-image-segment/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.064 [INFO] [RoutesResolver] AliyunSegmentImageController {/api/aliyun-segment-image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.064 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/segment, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.065 [INFO] [RouterExplorer] Mapped {/api/aliyun-segment-image/download, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.065 [INFO] [RoutesResolver] AiSpeechSynthesisController {/api/ai-speech-synthesis}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.065 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.065 [INFO] [RouterExplorer] Mapped {/api/ai-speech-synthesis/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.065 [INFO] [RoutesResolver] MinimaxTtsController {/api/minimax-tts}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.066 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/config, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.066 [INFO] [RouterExplorer] Mapped {/api/minimax-tts/generate, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.066 [INFO] [RoutesResolver] AiSpeechRecognitionController {/api/ai-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.066 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.066 [INFO] [RouterExplorer] Mapped {/api/ai-speech-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.067 [INFO] [RoutesResolver] XunfeiSpeechRecognitionController {/xunfei-speech-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.067 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-url, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.067 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-base64, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.067 [INFO] [RouterExplorer] Mapped {/xunfei-speech-recognition/recognize-file, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.067 [INFO] [RoutesResolver] TrainImageController {/api/scratch/train/image}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.068 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.068 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.068 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.068 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.068 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.069 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.069 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.069 [INFO] [RouterExplorer] Mapped {/api/scratch/train/image/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.069 [INFO] [RoutesResolver] ImageTrainModelController {/image-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.069 [INFO] [RouterExplorer] Mapped {/image-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.070 [INFO] [RouterExplorer] Mapped {/image-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.070 [INFO] [RouterExplorer] Mapped {/image-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.070 [INFO] [RouterExplorer] Mapped {/image-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.070 [INFO] [RouterExplorer] Mapped {/image-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.071 [INFO] [RouterExplorer] Mapped {/image-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.071 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.071 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.071 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.071 [INFO] [RouterExplorer] Mapped {/image-train-model/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.072 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.072 [INFO] [RouterExplorer] Mapped {/image-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.072 [INFO] [RoutesResolver] TrainPoseController {/api/scratch/train/pose}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.072 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.073 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.073 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.073 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.073 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.076 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.076 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.076 [INFO] [RouterExplorer] Mapped {/api/scratch/train/pose/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.077 [INFO] [RoutesResolver] PoseTrainModelController {/pose-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.077 [INFO] [RouterExplorer] Mapped {/pose-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.077 [INFO] [RouterExplorer] Mapped {/pose-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.077 [INFO] [RouterExplorer] Mapped {/pose-train-model/public, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.077 [INFO] [RouterExplorer] Mapped {/pose-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.078 [INFO] [RouterExplorer] Mapped {/pose-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.078 [INFO] [RouterExplorer] Mapped {/pose-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.078 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.078 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.079 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id/increment-use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.079 [INFO] [RouterExplorer] Mapped {/pose-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.079 [INFO] [RoutesResolver] TrainSoundController {/api/scratch/train/sound}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.080 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.080 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.080 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/search, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.080 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.081 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/status, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.081 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/:id, PUT} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.081 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/delete, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.081 [INFO] [RouterExplorer] Mapped {/api/scratch/train/sound/save, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.082 [INFO] [RoutesResolver] AudioTrainModelController {/audio-train-model}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.082 [INFO] [RouterExplorer] Mapped {/audio-train-model, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.082 [INFO] [RouterExplorer] Mapped {/audio-train-model, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.082 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.083 [INFO] [RouterExplorer] Mapped {/audio-train-model/creator/:creatorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.083 [INFO] [RouterExplorer] Mapped {/audio-train-model/type/:modelType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.083 [INFO] [RouterExplorer] Mapped {/audio-train-model/public/list, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.083 [INFO] [RouterExplorer] Mapped {/audio-train-model/class/:classId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.083 [INFO] [RouterExplorer] Mapped {/audio-train-model/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.084 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.084 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/use, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.084 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id/train, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.084 [INFO] [RouterExplorer] Mapped {/audio-train-model/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.084 [INFO] [RoutesResolver] AiObjectDetectionController {/api/ai-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.085 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.085 [INFO] [RouterExplorer] Mapped {/api/ai-object-detection/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.085 [INFO] [RoutesResolver] AliyunObjectDetectionController {/api/aliyun-object-detection}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.085 [INFO] [RouterExplorer] Mapped {/api/aliyun-object-detection/detect, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.085 [INFO] [RoutesResolver] AiStaticGestureRecognitionController {/api/scratch/ai-static-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.086 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.086 [INFO] [RouterExplorer] Mapped {/api/scratch/ai-static-gesture-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.086 [INFO] [RoutesResolver] AliyunStaticGestureRecognitionController {/api/aliyun-gesture-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.086 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/recognize, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.087 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/:taskId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.087 [INFO] [RouterExplorer] Mapped {/api/aliyun-gesture-recognition/status, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.087 [INFO] [RoutesResolver] UserPointsOfflineMessageController {/user-points-offline-message}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.087 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.087 [INFO] [RouterExplorer] Mapped {/user-points-offline-message, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.088 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.088 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/pending, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.088 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.088 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.089 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id/mark-as-sent, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.089 [INFO] [RouterExplorer] Mapped {/user-points-offline-message/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.089 [INFO] [RoutesResolver] UserRolePermissionController {/user-role-permission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.089 [INFO] [RouterExplorer] Mapped {/user-role-permission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.089 [INFO] [RouterExplorer] Mapped {/user-role-permission/batch-assign, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.090 [INFO] [RouterExplorer] Mapped {/user-role-permission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.090 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.090 [INFO] [RouterExplorer] Mapped {/user-role-permission/permission/:permissionId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.090 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.090 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.091 [INFO] [RouterExplorer] Mapped {/user-role-permission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.091 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/permission/:permissionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.091 [INFO] [RouterExplorer] Mapped {/user-role-permission/role/:roleId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.091 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.092 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.092 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.092 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.092 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.093 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.093 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.093 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.093 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.093 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.094 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.094 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.094 [INFO] [RoutesResolver] UserSchoolRelationController {/user-school-relation}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.094 [INFO] [RouterExplorer] Mapped {/user-school-relation, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.094 [INFO] [RouterExplorer] Mapped {/user-school-relation, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.095 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.095 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.095 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.095 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.095 [INFO] [RouterExplorer] Mapped {/user-school-relation/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.096 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.096 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.096 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id/role-type/:roleType, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.096 [INFO] [RouterExplorer] Mapped {/user-school-relation/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.096 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/school/:schoolId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.097 [INFO] [RouterExplorer] Mapped {/user-school-relation/user/:userId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.097 [INFO] [RouterExplorer] Mapped {/user-school-relation/school/:schoolId/all, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.097 [INFO] [RoutesResolver] UserWorkLikeController {/user-work-like}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.097 [INFO] [RouterExplorer] Mapped {/user-work-like, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.098 [INFO] [RouterExplorer] Mapped {/user-work-like, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.098 [INFO] [RouterExplorer] Mapped {/user-work-like/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.098 [INFO] [RouterExplorer] Mapped {/user-work-like/target/:targetId/type/:targetType, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.098 [INFO] [RouterExplorer] Mapped {/user-work-like/check, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.098 [INFO] [RouterExplorer] Mapped {/user-work-like/toggle, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.099 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.099 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.099 [INFO] [RouterExplorer] Mapped {/user-work-like/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.099 [INFO] [RoutesResolver] ActivityAuditController {/activity-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.099 [INFO] [RouterExplorer] Mapped {/activity-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.100 [INFO] [RouterExplorer] Mapped {/activity-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.100 [INFO] [RouterExplorer] Mapped {/activity-audit/activity/:activityId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.100 [INFO] [RouterExplorer] Mapped {/activity-audit/auditor/:auditorId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.100 [INFO] [RouterExplorer] Mapped {/activity-audit/result/:result, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.100 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.101 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.101 [INFO] [RouterExplorer] Mapped {/activity-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.101 [INFO] [RouterExplorer] Mapped {/activity-audit/:id/hard, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.101 [INFO] [RoutesResolver] ParticipationAuditController {/participation-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.101 [INFO] [RouterExplorer] Mapped {/participation-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.102 [INFO] [RouterExplorer] Mapped {/participation-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.102 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.102 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.102 [INFO] [RouterExplorer] Mapped {/participation-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.102 [INFO] [RoutesResolver] WorkAuditController {/work-audit}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.103 [INFO] [RouterExplorer] Mapped {/work-audit, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.103 [INFO] [RouterExplorer] Mapped {/work-audit, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.103 [INFO] [RouterExplorer] Mapped {/work-audit/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.103 [INFO] [RouterExplorer] Mapped {/work-audit/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.104 [INFO] [RouterExplorer] Mapped {/work-audit/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.104 [INFO] [RoutesResolver] StudentSelfAssessmentSubmissionController {/student-self-assessment-submission}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.104 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/bulk, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.104 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/assignment/:assignmentId/student/:studentId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.105 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/item/:itemId/submissions, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.105 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.105 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.105 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.106 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.106 [INFO] [RouterExplorer] Mapped {/student-self-assessment-submission/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.106 [INFO] [RoutesResolver] EncryptionController {/api/encryption}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.106 [INFO] [RouterExplorer] Mapped {/api/encryption/publicKey, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.106 [INFO] [RouterExplorer] Mapped {/api/encryption/keyStatus, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.107 [INFO] [RouterExplorer] Mapped {/api/encryption/session, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.107 [INFO] [RouterExplorer] Mapped {/api/encryption/secureSession, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.107 [INFO] [RouterExplorer] Mapped {/api/encryption/renew-session/:sessionId, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.107 [INFO] [RouterExplorer] Mapped {/api/encryption/session/:sessionId, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.108 [INFO] [RouterExplorer] Mapped {/api/encryption/session-stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.108 [INFO] [RouterExplorer] Mapped {/api/encryption/cleanup-sessions, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.108 [INFO] [RouterExplorer] Mapped {/api/encryption/create-session-debug, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.108 [INFO] [RoutesResolver] EncryptExampleController {/encrypt-example}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.108 [INFO] [RouterExplorer] Mapped {/encrypt-example/public-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.109 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.109 [INFO] [RouterExplorer] Mapped {/encrypt-example/partial-secure/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.109 [INFO] [RouterExplorer] Mapped {/encrypt-example/secure-with-decrypt, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.109 [INFO] [RouterExplorer] Mapped {/encrypt-example/test-request-body-encryption, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.110 [INFO] [RouterExplorer] Mapped {/encrypt-example/simple-partial/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.110 [INFO] [RoutesResolver] SecureExampleController {/api/secure-examples}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.110 [INFO] [RouterExplorer] Mapped {/api/secure-examples/standard, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.110 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.111 [INFO] [RouterExplorer] Mapped {/api/secure-examples/secure-partial, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.111 [INFO] [RouterExplorer] Mapped {/api/secure-examples/sessions/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.111 [INFO] [RoutesResolver] IpLocationController {/api/v1/ip-location}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.111 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/query, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.112 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/check-risk, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.112 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/user/:userId/stats, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.112 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/user/:userId/trust, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.112 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/current, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.112 [INFO] [RouterExplorer] Mapped {/api/v1/ip-location/health, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.113 [INFO] [RoutesResolver] TPSController {/tps}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.113 [INFO] [RouterExplorer] Mapped {/tps/create-test-students, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.113 [INFO] [RouterExplorer] Mapped {/tps/delete-by-prefix, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.113 [INFO] [RouterExplorer] Mapped {/tps/delete-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.113 [INFO] [RouterExplorer] Mapped {/tps/create-complete-test-data, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.114 [INFO] [RouterExplorer] Mapped {/tps/create-test-teachers, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.114 [INFO] [RouterExplorer] Mapped {/tps/export-test-data-csv, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.114 [INFO] [RouterExplorer] Mapped {/tps/assign-special-package-to-all-students, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.114 [INFO] [RoutesResolver] AiVoiceprintRecognitionController {/api/ai-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.114 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.115 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.115 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.115 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.115 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.115 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-libraries, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.116 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/:jobId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.116 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.116 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.116 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.116 [INFO] [RouterExplorer] Mapped {/api/ai-voiceprint-recognition/voiceprint-features/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.117 [INFO] [RoutesResolver] XunfeiVoiceprintRecognitionController {/xunfei-voiceprint-recognition}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.117 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.117 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/create-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.118 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/compare-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.118 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/search-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.118 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/query-feature-list, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.118 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/update-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.118 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.118 [INFO] [RouterExplorer] Mapped {/xunfei-voiceprint-recognition/delete-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.119 [INFO] [RoutesResolver] VoiceprintGroupController {/voiceprint-group}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.119 [INFO] [RouterExplorer] Mapped {/voiceprint-group, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.119 [INFO] [RouterExplorer] Mapped {/voiceprint-group, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.119 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.120 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.120 [INFO] [RouterExplorer] Mapped {/voiceprint-group/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.120 [INFO] [RoutesResolver] VoiceprintFeatureController {/voiceprint-feature}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.121 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.121 [INFO] [RouterExplorer] Mapped {/voiceprint-feature, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.121 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/user/:userId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.121 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/group/:groupId, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.121 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.122 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.122 [INFO] [RouterExplorer] Mapped {/voiceprint-feature/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.122 [INFO] [RoutesResolver] UserRoleController {/user-role}: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.122 [INFO] [RouterExplorer] Mapped {/user-role, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.122 [INFO] [RouterExplorer] Mapped {/user-role, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.123 [INFO] [RouterExplorer] Mapped {/user-role/active, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.123 [INFO] [RouterExplorer] Mapped {/user-role/default, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.123 [INFO] [RouterExplorer] Mapped {/user-role/code/:code, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.123 [INFO] [RouterExplorer] Mapped {/user-role/:id, GET} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.123 [INFO] [RouterExplorer] Mapped {/user-role/:id, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.124 [INFO] [RouterExplorer] Mapped {/user-role/:id/status/:status, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.124 [INFO] [RouterExplorer] Mapped {/user-role/:id/set-default, PATCH} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.124 [INFO] [RouterExplorer] Mapped {/user-role/:id, DELETE} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.124 [INFO] [RouterExplorer] Mapped {/user-role/condition, POST} route {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.125 [INFO] [PlatformCertificateService] 已加载微信支付平台证书: 604B78292D99D739AA8F4D437065F4D6A87F4057, 过期时间: 2030-06-30T10:22:39+08:00 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.125 [INFO] [PlatformCertificateService] 已加载 1 个微信支付平台证书 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.125 [INFO] [KeyManagementService] 密钥管理选项已加载: algorithm=aes-256-cbc, useRedis=true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.125 [INFO] [KeyManagementService] 从Redis加载了密钥: f2cd89fd-c713-4da3-81b2-63fbf1d264c0 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.126 [INFO] [KeyManagementService] 从Redis加载了密钥: 18a27d70-6f69-48f5-977d-a8333c052ab3 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.126 [INFO] [KeyManagementService] 从Redis加载了密钥: 401b349c-c3f8-4fee-9ff3-3cfa252b56b7 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.126 [INFO] [KeyManagementService] 从Redis加载了密钥: b0381a63-e168-4575-97d7-a67b68462d98 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.126 [INFO] [KeyManagementService] 从Redis加载了4个密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.126 [INFO] [KeyManagementService] 尝试从Redis加载RSA密钥对... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.127 [INFO] [KeyManagementService] 从Redis加载了1个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.127 [INFO] [KeyManagementService] 已从Redis加载1个RSA密钥对 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.127 [INFO] [KeyManagementService] 活跃RSA密钥对验证通过 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.127 [INFO] [KeyManagementService] RSA密钥对已初始化，共1个版本，活跃版本: key-0c386bf5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.128 [INFO] [KeyManagementService] 密钥管理服务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.128 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.128 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.128 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.129 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.129 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.129 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.129 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.130 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.130 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.130 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.130 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.130 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.131 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.131 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.131 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.131 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.132 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.132 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.132 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.132 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.132 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.133 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.133 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.133 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化开始... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.133 [INFO] [UserRoleTemplateTaskService] 执行用户角色模板启动任务... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.134 [INFO] [UserRoleTemplateTaskService] Template Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.134 [INFO] [UserRoleTemplateTaskService] Extension Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.134 [INFO] [UserRoleTemplateTaskService] Block Permission Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.134 [INFO] [UserRoleTemplateTaskService] Block Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.134 [INFO] [UserRoleTemplateTaskService] Extension Repository available: true {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.135 [INFO] [UserRoleTemplateTaskService] 开始检查数据库表结构... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.135 [INFO] [UserRoleTemplateTaskService] 数据库表 role_permission_templates 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.135 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_extension_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.135 [INFO] [UserRoleTemplateTaskService] 数据库表 role_template_block_permission 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.135 [INFO] [UserRoleTemplateTaskService] 数据库表 blocks 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.136 [INFO] [UserRoleTemplateTaskService] 数据库表 extensions 存在 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.136 [INFO] [UserRoleTemplateTaskService] RolePermissionTemplate 表中有 98 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.136 [INFO] [UserRoleTemplateTaskService] RoleTemplateExtensionPermission 表中有 2023 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.136 [INFO] [UserRoleTemplateTaskService] RoleTemplateBlockPermission 表中有 17659 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.137 [INFO] [UserRoleTemplateTaskService] Block 表中有 325 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.137 [INFO] [UserRoleTemplateTaskService] Extension 表中有 37 条记录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.137 [INFO] [UserRoleTemplateTaskService] 检查特殊权限模板... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.137 [INFO] [UserRoleTemplateTaskService] 开始获取系统中所有可用的扩展和积木块... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.137 [INFO] [UserRoleTemplateTaskService] 发现系统中有 37 个扩展和 325 个积木块 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.138 [INFO] [UserRoleTemplateTaskService] 特殊权限模板更新完成，确保所有积木和扩展权限都已启用 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.138 [INFO] [UserRoleTemplateTaskService] 用户角色模板任务初始化完成 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.138 [INFO] [NestApplication] Nest application successfully started {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:08:25.144 [INFO] [Startup] Application started successfully {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","url":"http://[::1]:8003","port":8003}
2025-07-31 17:10:00.015 [INFO] [QueueCleanupTask] 🧹 开始检查BullMQ队列键的过期时间设置 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.024 [INFO] [QueueCleanupTask] 🧹 开始检查BullMQ队列键的过期时间设置 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.035 [INFO] [QueueCleanupTask] 🧹 开始检查BullMQ队列键的过期时间设置 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.057 [INFO] [QueueCleanupTask] ✅ BullMQ键过期时间检查完成: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.058 [INFO] [QueueCleanupTask]    📊 总键数: 30 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.059 [INFO] [QueueCleanupTask]    🔧 新设置过期时间: 15个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.060 [INFO] [QueueCleanupTask]    ✔️ 已有过期时间: 15个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.060 [INFO] [QueueCleanupTask] ✅ BullMQ键过期时间检查完成: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.061 [INFO] [QueueCleanupTask]    📊 总键数: 30 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.062 [INFO] [QueueCleanupTask]    🔧 新设置过期时间: 15个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.062 [INFO] [QueueCleanupTask]    ✔️ 已有过期时间: 15个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.062 [INFO] [QueueCleanupTask] ✅ BullMQ键过期时间检查完成: {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.063 [INFO] [QueueCleanupTask]    📊 总键数: 30 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.064 [INFO] [QueueCleanupTask]    🔧 新设置过期时间: 14个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.065 [INFO] [QueueCleanupTask]    ✔️ 已有过期时间: 16个 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.277 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.296 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:00.311 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/ping","statusCode":200,"responseTime":"33ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:04.226 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:04.233 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:04.236 [INFO] [Console] 🔐 [LoginLog] IP地址获取详情: {
  "请求路径": "/api/user-auth/password",
  "请求方法": "POST",
  "请求头IP信息": {},
  "连接IP信息": {
    "connection.remoteAddress": "::ffff:127.0.0.1",
    "socket.remoteAddress": "::ffff:127.0.0.1"
  },
  "时间戳": "2025-07-31T09:10:04.235Z"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:04.238 [INFO] [Console] ✅ [LoginLog] IP地址确定: {
  "最终IP": "::ffff:127.0.0.1",
  "是否为默认": false,
  "时间戳": "2025-07-31T09:10:04.237Z"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:04.273 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/user-auth/password","statusCode":200,"responseTime":"47ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:05.594 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.595 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.597 [INFO] [Console] 🔐 [LoginLog] IP地址获取详情: {
  "请求路径": "/api/user-auth/select-identity",
  "请求方法": "POST",
  "请求头IP信息": {},
  "连接IP信息": {
    "connection.remoteAddress": "::ffff:127.0.0.1",
    "socket.remoteAddress": "::ffff:127.0.0.1"
  },
  "时间戳": "2025-07-31T09:10:05.596Z"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.598 [INFO] [Console] ✅ [LoginLog] IP地址确定: {
  "最终IP": "::ffff:127.0.0.1",
  "是否为默认": false,
  "时间戳": "2025-07-31T09:10:05.598Z"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.608 [INFO] [Console]  {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.611 [INFO] [Console] 🔄 检测到用户已有活跃会话，将记录被动登出 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.614 [INFO] [Console] 🚪 [LOGOUT] {
  "timestamp": "2025-07-31T09:10:05.613Z",
  "level": "INFO",
  "event": "LOGOUT",
  "userId": 5,
  "clientIp": "::ffff:127.0.0.1",
  "reason": "新设备登录，旧会话被挤出",
  "logoutType": "passive"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.615 [INFO] [Console] 🚪 开始记录登出，参数: {
  "userId": 5,
  "reason": "新设备登录，旧会话被挤出",
  "logoutType": "passive"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.633 [INFO] [Console] 📊 该用户最近5条登录记录: [
  {
    "id": "191",
    "loginType": "password",
    "loginStatus": "success",
    "sessionId": "a1bf715f-d669-4f43-8403-e2ed6d668d8e",
    "loginTime": "2025-07-31T09:02:14.000Z",
    "logoutTime": null
  },
  {
    "id": "190",
    "loginType": "password",
    "loginStatus": "success",
    "sessionId": "f1679f45-886b-462a-9c12-aea7f39ec891",
    "loginTime": "2025-07-31T08:59:16.000Z",
    "logoutTime": "2025-07-31T09:02:14.000Z"
  },
  {
    "id": "188",
    "loginType": "password",
    "loginStatus": "success",
    "sessionId": "8ac2c296-4728-42b7-92c7-b67028e6aacb",
    "loginTime": "2025-07-31T08:58:00.000Z",
    "logoutTime": "2025-07-31T08:59:16.000Z"
  },
  {
    "id": "187",
    "loginType": "password",
    "loginStatus": "success",
    "sessionId": "cfc95a8b-a9e2-488f-9473-df507cb4f4bb",
    "loginTime": "2025-07-31T08:57:37.000Z",
    "logoutTime": "2025-07-31T08:58:00.000Z"
  },
  {
    "id": "185",
    "loginType": "password",
    "loginStatus": "success",
    "sessionId": "89063dbc-1ee4-4771-ba8c-14285034929e",
    "loginTime": "2025-07-31T06:39:32.000Z",
    "logoutTime": "2025-07-31T08:57:37.000Z"
  }
] {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.635 [INFO] [Console] 🔍 没有sessionId，查找用户最近的登录记录... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.651 [INFO] [Console] 📋 查找到的最近登录记录: {
  "id": "191",
  "userId": "5",
  "sessionId": "a1bf715f-d669-4f43-8403-e2ed6d668d8e",
  "loginTime": "2025-07-31T09:02:14.000Z",
  "loginStatus": "success"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.652 [INFO] [Console] 💾 开始更新最近登录记录的登出信息... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.673 [INFO] [Console] ✅ 最近登录记录的登出日志更新成功: {
  "userId": 5,
  "duration": "471秒",
  "logoutTime": "2025-07-31T09:10:05.652Z"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.674 [INFO] [Console] ✅ 被动登出日志记录成功: {
  "userId": 5,
  "reason": "新设备登录，旧会话被挤出"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.679 [INFO] [Console] ✅ [LOGIN_SUCCESS] {
  "timestamp": "2025-07-31T09:10:05.676Z",
  "level": "INFO",
  "event": "LOGIN_SUCCESS",
  "userId": 5,
  "loginType": "password",
  "clientIp": "::ffff:127.0.0.1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Sa...",
  "deviceInfo": "桌面设备 - Windows - Chrome",
  "sessionId": "8c46b164-bc56-4d52-9c77-d27a55da3805"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.681 [INFO] [Console] 🔍 检查服务实例: {
  "hasService": true,
  "serviceType": "UserLoginLogService"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.682 [INFO] [Console] 📝 开始保存登录成功日志到数据库... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.683 [INFO] [Console] 🚀 开始记录登录日志，输入数据: {
  "userId": 5,
  "loginType": "password",
  "loginStatus": "success",
  "clientIp": "::ffff:127.0.0.1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "deviceInfo": "桌面设备 - Windows - Chrome",
  "sessionId": "8c46b164-bc56-4d52-9c77-d27a55da3805"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.685 [INFO] [Console] 📝 创建的登录日志实体: {
  "userId": 5,
  "loginType": "password",
  "loginStatus": "success",
  "clientIp": "::ffff:127.0.0.1",
  "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
  "deviceInfo": "桌面设备 - Windows - Chrome",
  "sessionId": "8c46b164-bc56-4d52-9c77-d27a55da3805",
  "loginTime": "2025-07-31T09:10:05.684Z"
} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.686 [INFO] [Console] 💾 开始保存到数据库... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.763 [INFO] [Console] ✅ 登录日志保存成功，ID: 192 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.764 [INFO] [Console] ✅ 登录成功日志已保存到数据库 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:05.766 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/user-auth/select-identity","statusCode":201,"responseTime":"171ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.673 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.711 [INFO] [EncryptionService] 【密钥交换】客户端请求RSA公钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.718 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/encryption/publicKey","statusCode":200,"responseTime":"41ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.746 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.752 [DEBUG] [HTTP] Incoming GET /?phone=15270736300 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.754 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.759 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.763 [DEBUG] [HTTP] Incoming GET /?userId=5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.770 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.777 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.779 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.815 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web-carousel/active","statusCode":304,"responseTime":"38ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.817 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.842 [INFO] [EncryptionService] 【密钥交换】客户端请求RSA公钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.845 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/encryption/publicKey","statusCode":304,"responseTime":"28ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.849 [DEBUG] [HTTP] Incoming GET /?type=2&orderBy=popular&status=1&page=1&size=10 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.851 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.860 [INFO] [Console] {} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.880 [WARN] [UserAuthController] 未找到用户5的学生信息: 用户ID 5 的学生记录未找到 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.891 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/publishedIdsByTarget","statusCode":304,"responseTime":"120ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.897 [DEBUG] [HTTP] Incoming GET /?type=1&orderBy=popular&status=1&page=1&size=10 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.897 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.902 [INFO] [Console] {} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.913 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"153ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.918 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/list","statusCode":201,"responseTime":"172ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.927 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-auth/findAllBindByPhone?phone=15270736300","statusCode":304,"responseTime":"174ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.928 [DEBUG] [HTTP] Incoming GET /?studentId=5&roleId=2&page=1&size=50 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.934 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.974 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":200,"responseTime":"211ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:07.983 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:07.987 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.016 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.067 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web-carousel/active","statusCode":304,"responseTime":"84ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.103 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/publishedIdsByTarget","statusCode":304,"responseTime":"88ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.105 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.113 [DEBUG] [HTTP] Incoming GET /?phone=15270736300 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.115 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.145 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"295ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.148 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/list","statusCode":201,"responseTime":"214ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.151 [INFO] [Console] 用户ID 2 不存在，使用默认用户信息 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.161 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.171 [DEBUG] [HTTP] Incoming GET /?userId=5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.205 [INFO] [EncryptionController] 创建会话密钥，会话ID: 1753953008203.ffff127001.jt3hao6eyvd {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.206 [INFO] [EncryptionService] 【会话创建】开始为会话 1753953008203.ffff127001.jt3hao6eyvd 创建标准密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.207 [INFO] [EncryptionService] 【会话创建】接收到加密的AES密钥数据，长度: 344, 密钥ID: key-0c386bf5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.207 [INFO] [EncryptionService] 【会话创建】使用指定密钥ID key-0c386bf5 解密AES密钥... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.212 [INFO] [EncryptionService] 【会话创建】成功解析AES密钥数据 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.213 [INFO] [EncryptionService] 【会话创建】生成标准会话密钥，AES密钥指纹: b34a8a8d, IV指纹: 83c8246e {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.213 [INFO] [EncryptionService] 【会话创建】会话将于 2025-07-31T09:40:08.212Z 过期 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.266 [INFO] [RedisSessionService] 会话 1753953008203.ffff127001.jt3hao6eyvd 已存储到Redis (类型: standard, TTL: 1800秒) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.266 [INFO] [EncryptionService] 【会话创建】标准会话 1753953008203.ffff127001.jt3hao6eyvd 创建成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.268 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/encryption/session","statusCode":201,"responseTime":"107ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.273 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=1&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"376ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.286 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"181ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.293 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.297 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.301 [DEBUG] [HTTP] Incoming GET /?type=2&orderBy=popular&status=1&page=1&size=10 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.302 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.308 [INFO] [Console] {} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.334 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/teacher-task/list?studentId=5&roleId=2&page=1&size=50","statusCode":304,"responseTime":"405ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.340 [WARN] [UserAuthController] 未找到用户5的学生信息: 用户ID 5 的学生记录未找到 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.344 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-auth/findAllBindByPhone?phone=15270736300","statusCode":304,"responseTime":"231ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.354 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.359 [INFO] [EncryptionController] 创建会话密钥，会话ID: 1753953008359.ffff127001.24dtlugp1ow {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.360 [INFO] [EncryptionService] 【会话创建】开始为会话 1753953008359.ffff127001.24dtlugp1ow 创建标准密钥 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.361 [INFO] [EncryptionService] 【会话创建】接收到加密的AES密钥数据，长度: 344, 密钥ID: key-0c386bf5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.361 [INFO] [EncryptionService] 【会话创建】使用指定密钥ID key-0c386bf5 解密AES密钥... {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.368 [INFO] [EncryptionService] 【会话创建】成功解析AES密钥数据 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.371 [INFO] [EncryptionService] 【会话创建】生成标准会话密钥，AES密钥指纹: 3dc3fbc8, IV指纹: 542eac03 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.372 [INFO] [EncryptionService] 【会话创建】会话将于 2025-07-31T09:40:08.369Z 过期 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.376 [DEBUG] [HTTP] Incoming GET /?type=1&orderBy=popular&status=1&page=1&size=10 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.377 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.381 [INFO] [Console] {} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.383 [INFO] [RedisSessionService] 会话 1753953008359.ffff127001.24dtlugp1ow 已存储到Redis (类型: standard, TTL: 1800秒) {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.383 [INFO] [EncryptionService] 【会话创建】标准会话 1753953008359.ffff127001.24dtlugp1ow 创建成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.385 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/encryption/session","statusCode":201,"responseTime":"31ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.388 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.395 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"224ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.419 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.464 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"45ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.474 [DEBUG] [HTTP] Incoming GET /?userId=5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.487 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"98ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.497 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"196ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.507 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.517 [INFO] [Console] 用户ID 2 不存在，使用默认用户信息 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.533 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.572 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=1&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"196ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.579 [DEBUG] [HTTP] Incoming GET /?type=2&orderBy=popular&status=1&page=1&size=10 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.582 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.587 [INFO] [Console] {} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.598 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":304,"responseTime":"91ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.603 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"128ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.605 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.611 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.619 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/read/unread","statusCode":201,"responseTime":"320ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.628 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/total","statusCode":304,"responseTime":"96ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.633 [DEBUG] [HTTP] Incoming GET /?userId=5 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.654 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"49ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.658 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/read/unread","statusCode":201,"responseTime":"366ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.674 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":304,"responseTime":"63ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.700 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-point/packages?userId=5","statusCode":304,"responseTime":"63ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.717 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"136ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.719 [DEBUG] [HTTP] Incoming GET /?id=33 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.732 [DEBUG] [HTTP] Incoming GET /?type=2&orderBy=popular&status=1&page=1&size=10 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.734 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.742 [INFO] [Console] {} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.779 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.804 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/detail?id=33","statusCode":200,"responseTime":"81ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.805 [DEBUG] [HTTP] Incoming GET /?id=33 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.825 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"47ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.832 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/announcement/detail?id=33","statusCode":304,"responseTime":"25ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.874 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/user-work-info/public?type=2&orderBy=popular&status=1&page=1&size=10","statusCode":304,"responseTime":"149ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.875 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.898 [DEBUG] [WebSocketGateway] 客户端已连接: W3S7Ll8Bs3LVYYznAAAB {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.910 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"34ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:08.914 [INFO] [Console] 验证令牌 - 用户ID: 5, 存储的令牌: {"id":5,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc1JlZnJlc2giOmZhbHNlLCJpZCI6NSwiYXZhdGFyVXJsIjoiaHR0cHM6Ly9sb2dpY2xlYXAub3NzLWNuLWd1YW5nemhvdS5hbGl5dW5jcy5jb20vdXBsb2Fkcy8xNzQ3NzI0MTUzODY0LWRtOHRjN2MtamF5LmpwZyIsIm5pY2tOYW1lIjoi55-z5qa0IiwicGhvbmUiOiIxNTI3MDczNjMwMCIsInBhc3N3b3JkIjoiZTEwYWRjMzk0OWJhNTlhYmJlNTZlMDU3ZjIwZjg4M2UiLCJwb2ludHMiOiI0OTAwIiwicG9pbnRzRXhwaXJlVGltZSI6IjIwMjUtMDgtMDhUMDY6MjE6MDAuMDAwWiIsImludHJvZHVjdGlvbiI6IuS9oOWlveOAguOAguOAglxuIiwicm9sZUlkIjoyLCJyb2xlIjp7ImlkIjoyLCJuYW1lIjoi5pWZ5biIIiwiY29kZSI6IjIiLCJzdGF0dXMiOjEsInNvcnQiOjIsImRlc2NyaXB0aW9uIjpudWxsLCJ0eXBlIjoyLCJpc0RlZmF1bHQiOjAsImNyZWF0ZVRpbWUiOiIyMDI1LTAxLTIwVDEwOjU1OjE0LjYwMloiLCJ1cGRhdGVUaW1lIjoiMjAyNS0wMS0yMFQxMDo1NToxNC42MDJaIn0sIndlaXhpbl9vcGVuaWQiOiJvMlZQZzdYMFNtdEVVR3hteFduZHI2c2NrbEtJIiwiZ2VuZGVyIjowLCJyZWdpc3Rlcl90eXBlIjoicGhvbmUiLCJzdGF0dXMiOjEsInN0dWRlbnQiOm51bGwsImNyZWF0ZVRpbWUiOiIyMDI1LTA1LTI3VDA5OjEzOjU3LjI4N1oiLCJ1cGRhdGVUaW1lIjoiMjAyNS0wNy0wOVQwNjoyMDo1OS4yMTZaIiwiaWF0IjoxNzUzOTUzMDA1LCJleHAiOjE3NTQwMzk0MDV9.AivHfedsx9_By0e4zJLX49o1LZhKOoBOhvNGlAe1yII","refreshToken":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc1JlZnJlc2giOnRydWUsImlkIjo1LCJhdmF0YXJVcmwiOiJodHRwczovL2xvZ2ljbGVhcC5vc3MtY24tZ3Vhbmd6aG91LmFsaXl1bmNzLmNvbS91cGxvYWRzLzE3NDc3MjQxNTM4NjQtZG04dGM3Yy1qYXkuanBnIiwibmlja05hbWUiOiLnn7PmprQiLCJwaG9uZSI6IjE1MjcwNzM2MzAwIiwicGFzc3dvcmQiOiJlMTBhZGMzOTQ5YmE1OWFiYmU1NmUwNTdmMjBmODgzZSIsInBvaW50cyI6IjQ5MDAiLCJwb2ludHNFeHBpcmVUaW1lIjoiMjAyNS0wOC0wOFQwNjoyMTowMC4wMDBaIiwiaW50cm9kdWN0aW9uIjoi5L2g5aW944CC44CC44CCXG4iLCJyb2xlSWQiOjIsInJvbGUiOnsiaWQiOjIsIm5hbWUiOiLmlZnluIgiLCJjb2RlIjoiMiIsInN0YXR1cyI6MSwic29ydCI6MiwiZGVzY3JpcHRpb24iOm51bGwsInR5cGUiOjIsImlzRGVmYXVsdCI6MCwiY3JlYXRlVGltZSI6IjIwMjUtMDEtMjBUMTA6NTU6MTQuNjAyWiIsInVwZGF0ZVRpbWUiOiIyMDI1LTAxLTIwVDEwOjU1OjE0LjYwMloifSwid2VpeGluX29wZW5pZCI6Im8yVlBnN1gwU210RVVHeG14V25kcjZzY2tsS0kiLCJnZW5kZXIiOjAsInJlZ2lzdGVyX3R5cGUiOiJwaG9uZSIsInN0YXR1cyI6MSwic3R1ZGVudCI6bnVsbCwiY3JlYXRlVGltZSI6IjIwMjUtMDUtMjdUMDk6MTM6NTcuMjg3WiIsInVwZGF0ZVRpbWUiOiIyMDI1LTA3LTA5VDA2OjIwOjU5LjIxNloiLCJpYXQiOjE3NTM5NTMwMDUsImV4cCI6MTc1NjU0NTAwNX0.C6nKqSLAVHvBA2K4nWpmYXXnrcjF5JjKo2Mt5vkwueM","nickName":"石榴","avatarUrl":"https://logicleap.oss-cn-guangzhou.aliyuncs.com/uploads/1747724153864-dm8tc7c-jay.jpg","phone":"15270736300","gender":0,"roleId":2} {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.918 [DEBUG] [WebSocketService] 客户端已添加: W3S7Ll8Bs3LVYYznAAAB {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.920 [DEBUG] [WebSocketGateway] 用户 5 连接成功 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.934 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.940 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.946 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:08.969 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"30ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:09.053 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/increaseReadCount","statusCode":201,"responseTime":"120ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:09.067 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/increaseReadCount","statusCode":201,"responseTime":"122ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:09.074 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:09.114 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/user-work-like/user/5","statusCode":304,"responseTime":"39ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:09.134 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:09.174 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/5","statusCode":304,"responseTime":"40ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:11.282 [DEBUG] [HTTP] Incoming POST / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:11.288 [ERROR] [GlobalExceptionFilter] POST /api/web/announcement/read/mark - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (F:\logicleap2\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (F:\logicleap2\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (F:\logicleap2\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at F:\logicleap2\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (F:\logicleap2\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (F:\logicleap2\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (F:\logicleap2\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (F:\logicleap2\logic-back\node_modules\router\index.js:435:11)
2025-07-31 17:10:11.289 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
{"url":"/api/web/announcement/read/mark","method":"POST","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T09:10:11.289Z"}
2025-07-31 17:10:11.291 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"POST","url":"/api/web/announcement/read/mark","statusCode":401,"responseTime":"9ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:10:30.244 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:30.247 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:10:30.251 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/ping","statusCode":200,"responseTime":"8ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:11:00.197 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:11:00.198 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:11:00.200 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/ping","statusCode":200,"responseTime":"3ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
2025-07-31 17:11:30.290 [DEBUG] [HTTP] Incoming GET / {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:11:30.291 [INFO] [Console] 不验证登录 {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG"}
2025-07-31 17:11:30.294 [INFO] [HTTP] HTTP Request {"service":"logic-back","version":"0.0.1","environment":"dev","pid":35884,"hostname":"DESKTOP-1L38AEG","method":"GET","url":"/api/web/user/info/ping","statusCode":200,"responseTime":"4ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","ip":"::ffff:127.0.0.1"}
