import { Controller, Get, Query, Put, Body, Param, HttpStatus, BadRequestException, NotFoundException, Post } from '@nestjs/common';
import { WebUserInfoService } from './web_user_info.service';
import { ApiOperation, ApiQuery, ApiResponse, ApiParam, ApiBody, ApiTags } from '@nestjs/swagger';
import { UserInfoService } from 'src/util/database/mysql/user_info/user_info.service';
import { UserPasswordResetRequestService } from 'src/util/database/mysql/user_password_reset_request/user_password_reset_request.service';
import { NotLogin } from '../router_guard/not-login.decorator';
import { HttpResponseResultService } from '../http_response_result/http_response_result.service';
import { Encrypt, SecureEncrypt } from '../../util/encrypt/encrypt.decorator';


@ApiTags('api/web/用户信息(web/user/info)')
@Controller('api/web/user/info')
export class WebUserInfoController {
  constructor(
    private readonly webUserInfoService: WebUserInfoService,
    private readonly userInfoService: UserInfoService,
    private readonly passwordResetRequestService: UserPasswordResetRequestService,
    private readonly httpResponseResultService: HttpResponseResultService
  ) { }

  @ApiOperation({ summary: '心跳检测', description: '用于客户端检测网络连接状态' })
  @ApiResponse({ status: 200, description: '服务器正常运行' })
  @NotLogin()
  @Get('/ping')
  async ping() {
    return this.httpResponseResultService.success({
      msg: 'pong',
      data: {
        timestamp: new Date().getTime(),
        status: 'ok'
      }
    });
  }

  // 查询userId数组里是否有绑定手机号
  @ApiOperation({ summary: '心跳检测', description: '用于客户端检测网络连接状态' })
  @ApiResponse({ status: 200, description: '服务器正常运行' })
  @NotLogin()
  @Post('/checkHasBindPhone')
  async checkHasBindPhone(@Body() selectedStudents: number[]) {
    const result = await this.userInfoService.checkHasBindPhone(selectedStudents);
    return this.httpResponseResultService.success(result, '检查手机号绑定状态成功');
  }

  @ApiOperation({ summary: '获取用户基础信息', description: '根据用户ID获取用户信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @Get('/:id')
  @Encrypt()
  async getStudentInfo(@Param('id') id: number) {
    const userInfo = await this.userInfoService.findOne(id);
    // 这里额外做判断，如果password字段为空， 附带标志位需要设置密码，给NavBar进行判断显示
    let needSetPwd=false
    if (userInfo.password === null||userInfo.password.trim()==="") {
      console.log("用户密码为空");
       needSetPwd=true
    }
    if (!userInfo) {
      throw new NotFoundException('用户不存在');
    }
    

    return {
      ...userInfo,
      needSetPwd
    };
  }

  @ApiOperation({ summary: '更新用户信息', description: '更新用户的昵称、头像和简介' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', description: '用户ID' },
        nickName: { type: 'string', description: '用户昵称' },
        avatarUrl: { type: 'string', description: '用户头像URL' },
        introduction: { type: 'string', description: '用户简介' }
      }
    }
  })
  @ApiResponse({ status: 200, description: '更新成功' })
  @Put('/update')
  async updateUserInfo(
    @Body() updateData: {
      id: number;
      nickName?: string;
      avatarUrl?: string;
      introduction?: string;
    }
  ) {
    const result = await this.userInfoService.update(updateData.id, updateData);
    return {
      nickName: result.nickName,
      avatarUrl: result.avatarUrl,
      introduction: result.introduction
    };
  }

  @ApiOperation({ summary: '更新用户密码', description: '验证旧密码并更新为新密码' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number', description: '用户ID' },
        oldPassword: { type: 'string', description: '旧密码' },
        newPassword: { type: 'string', description: '新密码' }
      },
      required: ['id', 'oldPassword', 'newPassword']
    }
  })
  @ApiResponse({ status: 200, description: '密码更新成功' })
  @ApiResponse({ status: 400, description: '旧密码验证失败' })
  @ApiResponse({ status: 404, description: '用户不存在' })
  @SecureEncrypt({
    enabled: true,
    decryptRequest: true
  })
  @Put('/update/password')
  async updatePassword(
    @Body() passwordData: {
      id: number;
      oldPassword: string;
      newPassword: string;
    }
  ) {
    // 1. 查询用户信息
    const userInfo = await this.userInfoService.findOne(passwordData.id);

    // 2. 验证旧密码
    const isPasswordValid = this.webUserInfoService.validatePassword(
      userInfo.password,
      passwordData.oldPassword
    );

    if (!isPasswordValid) {
      throw new BadRequestException('旧密码验证失败');
    }

    // 3. 加密新密码
    const encryptedPassword = this.webUserInfoService.encryptPassword(passwordData.newPassword);

    // 4. 更新密码
    await this.userInfoService.update(passwordData.id, {
      password: encryptedPassword
    });

    return {
      code: HttpStatus.OK,
      msg: '密码更新成功',
      data: null
    };
  }

  @ApiOperation({ summary: '提交密码重置申请', description: '用户忘记密码时提交重置申请' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        phone: { type: 'string', description: '用户手机号' },
        name: { type: 'string', description: '用户姓名' },
        contactInfo: { type: 'string', description: '联系方式' },
        remark: { type: 'string', description: '备注信息' },
        schoolName: { type: 'string', description: '学校名称(针对学生用户)' },
        studentNumber: { type: 'string', description: '学号(针对学生用户)' }
      },
      required: ['phone', 'name']
    }
  })

  @NotLogin()
  @ApiResponse({ status: 200, description: '申请提交成功' })
  @Post('reset_password/request')
  async requestPasswordReset(
    @Body() requestData: {
      phone: string;
      name: string;
      contactInfo?: string;
      remark?: string;
      schoolName?: string;
      studentNumber?: string;
    }
  ) {
    // 1. 调用服务准备请求数据
    const resetRequestData = this.webUserInfoService.preparePasswordResetRequestData(requestData);

    // 2. 调用数据库服务创建请求
    const request = await this.passwordResetRequestService.create(resetRequestData);
    return this.httpResponseResultService.success({
      msg: '密码重置申请已提交',
      data: {
        requestId: request.id
      }
    });
  }

  @ApiOperation({ summary: '处理密码重置申请', description: '管理员处理用户的密码重置申请' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        requestId: { type: 'number', description: '密码重置申请ID' },
        status: { type: 'number', description: '处理状态：1-同意，2-拒绝' },
        handleRemark: { type: 'string', description: '处理备注' },
        handlerId: { type: 'number', description: '处理人ID' }
      },
      required: ['requestId', 'status', 'handlerId']
    }
  })
  @ApiResponse({ status: 200, description: '处理成功' })
  @ApiResponse({ status: 404, description: '申请不存在' })
  @Put('/reset/password/handle')
  async handlePasswordReset(
    @Body() handleData: {
      requestId: number;
      status: number;
      handleRemark?: string;
      handlerId: number;
    }
  ) {
    // 1. 获取重置请求
    const request = await this.passwordResetRequestService.findOne(handleData.requestId);

    // 2. 调用服务处理请求数据
    const updatedRequest = this.webUserInfoService.preparePasswordResetHandleData(
      request,
      handleData
    );

    // 3. 根据状态处理
    if (handleData.status === 2) {
      // 拒绝请求
      await this.passwordResetRequestService.update(updatedRequest.id, updatedRequest);

      return {
        code: HttpStatus.OK,
        msg: '已拒绝密码重置申请',
        data: null
      };
    } else if (handleData.status === 1) {
      try {
        // 同意请求，重置密码
        const userInfo = await this.userInfoService.findByPhone(request.phone);

        // 检查用户是否存在
        if (!userInfo) {
          return this.httpResponseResultService.error('用户不存在', null, 404);
        }

        // 获取默认密码并加密
        const defaultPassword = this.webUserInfoService.getDefaultPassword();
        const encryptedPassword = this.webUserInfoService.encryptPassword(defaultPassword);

        // 更新用户密码
        await this.userInfoService.update(userInfo.id, {
          password: encryptedPassword
        });

        // 更新请求状态
        await this.passwordResetRequestService.update(updatedRequest.id, updatedRequest);

        return {
          code: HttpStatus.OK,
          msg: `密码已重置为${defaultPassword}`,
          data: null
        };
      } catch (error) {
        throw new BadRequestException(error.message || '重置密码失败');
      }
    } else {
      throw new BadRequestException('无效的处理状态');
    }
  }

  @ApiOperation({ summary: '获取密码重置申请列表', description: '管理员获取密码重置申请列表' })
  @ApiQuery({ name: 'status', description: '状态过滤：0-待处理，1-已同意，2-已拒绝', required: false })
  @ApiQuery({ name: 'page', description: '页码', required: false })
  @ApiQuery({ name: 'size', description: '每页数量', required: false })
  @ApiResponse({ status: 200, description: '获取成功' })
  @Get('/reset/password/list')
  async getPasswordResetList(
    @Query('status') status?: number,
    @Query('page') page: number = 1,
    @Query('size') size: number = 10
  ) {
    // 1. 获取所有请求
    const requests = await this.passwordResetRequestService.findAll();

    // 2. 调用服务进行分页和过滤
    const result = this.webUserInfoService.paginateAndFilterRequests(
      requests,
      status,
      page,
      size
    );

    return {
      code: HttpStatus.OK,
      data: result
    };
  }
}


