"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const logger_service_1 = require("./logger.service");
let GlobalExceptionFilter = class GlobalExceptionFilter {
    loggerService;
    constructor(loggerService) {
        this.loggerService = loggerService;
    }
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        let status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
        let message = 'Internal server error';
        let details = {};
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'string') {
                message = exceptionResponse;
            }
            else if (typeof exceptionResponse === 'object') {
                message = exceptionResponse.message || exception.message;
                details = exceptionResponse;
            }
        }
        else if (exception instanceof Error) {
            message = exception.message;
            details = {
                name: exception.name,
                stack: exception.stack
            };
        }
        this.loggerService.error(`${request.method} ${request.url} - ${message}`, exception instanceof Error ? exception.stack : undefined, 'GlobalExceptionFilter');
        this.loggerService.error('Exception Details', JSON.stringify({
            url: request.url,
            method: request.method,
            statusCode: status,
            message,
            details,
            userAgent: request.get('User-Agent'),
            ip: request.ip,
            timestamp: new Date().toISOString()
        }), 'GlobalExceptionFilter');
        response.status(status).json({
            code: status,
            msg: message,
            data: details || null
        });
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = __decorate([
    (0, common_1.Catch)(),
    __metadata("design:paramtypes", [logger_service_1.LoggerService])
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map