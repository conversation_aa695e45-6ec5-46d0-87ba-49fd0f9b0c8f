import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { EncryptionService } from './encryption.service';

/**
 * 加密服务定期清理任务
 * 用于清理过期的会话密钥和其他临时数据
 */
@Injectable()
export class EncryptionCleanupTask {
  private readonly logger = new Logger(EncryptionCleanupTask.name);
  
  constructor(private readonly encryptionService: EncryptionService) {
    this.logger.log('【初始化】会话清理任务已启动');
  }
  
  /**
   * 每小时执行一次会话清理
   * 清理过期的会话密钥
   */
  @Cron('0 0 */1 * * *') // 每小时执行一次
  async handleSessionCleanup() {
    this.logger.log('【定时任务】开始执行会话清理任务');
    
    try {
      await this.encryptionService.cleanupExpiredSessions();
      this.logger.log('【定时任务】会话清理任务完成');
    } catch (error) {
      this.logger.error(`【定时任务】会话清理任务失败: ${error.message}`, error.stack);
    }
  }
  
  /**
   * 每天凌晨3点运行一次，输出会话统计信息
   */
  @Cron('0 0 3 * * *') // 每天凌晨3点
  async logDailyStats() {
    this.logger.log('【定时任务】每日统计 - 开始');
    
    try {
      await this.encryptionService.logSessionStats();
      this.logger.log('【定时任务】每日统计 - 完成');
    } catch (error) {
      this.logger.error(`【定时任务】每日统计失败: ${error.message}`, error.stack);
    }
  }
} 